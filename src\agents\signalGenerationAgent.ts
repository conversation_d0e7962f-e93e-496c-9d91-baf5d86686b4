import { OrderBook, OHLCV } from 'ccxt';

// Define a simplified structure for signals
export interface TradeSignal {
  type: 'long' | 'short' | 'hold';
  reason: string;
  confidence?: number; // Optional: 0-1
  timestamp: number;
  priceAtSignal?: number; // Price at the time of signal generation
  keyLevel?: number; // Identified key support/resistance level
  imbalanceRatio?: number; // Bid/Ask imbalance ratio
  cvdDirection?: 'rising' | 'falling' | 'flat';
  candlestickPattern?: 'bullish_pin_bar' | 'bearish_pin_bar' | 'bullish_engulfing' | 'bearish_engulfing' | 'none';
}

const IMBALANCE_RATIO_THRESHOLD = 3;
const TICKS_WITHIN_PRICE = 5; // Number of price levels to check for imbalance

/**
 * Analyzes order book data to detect significant bid/ask imbalances.
 * @param orderBook The current order book.
 * @param currentPrice The current market price (e.g., last trade price).
 * @returns An object indicating imbalance type ('bid', 'ask', or 'none') and the ratio.
 */
export function detectOrderBookImbalance(orderBook: OrderBook | null, currentPrice: number): { type: 'bid' | 'ask' | 'none', ratio: number, nearPriceLevel: number | null } {
  if (!orderBook || !orderBook.bids || !orderBook.asks || orderBook.bids.length === 0 || orderBook.asks.length === 0) {
    return { type: 'none', ratio: 1, nearPriceLevel: null };
  }

  // Consider bids/asks within a certain range of the current price
  // For simplicity, let's sum liquidity within the top N ticks/pips
  // A more precise implementation would use actual price steps (tick size)

  let totalBidVolumeNearPrice = 0;
  let totalAskVolumeNearPrice = 0;
  let closestBidPriceLevel: number | null = null;
  let closestAskPriceLevel: number | null = null;

  // Sum bid volume within TICKS_WITHIN_PRICE levels from the best bid
  for (let i = 0; i < Math.min(orderBook.bids.length, TICKS_WITHIN_PRICE); i++) {
    const bidPrice = orderBook.bids[i][0];
    const bidAmount = orderBook.bids[i][1];
    if (typeof bidAmount === 'number') {
      totalBidVolumeNearPrice += bidAmount;
    }
    if (i === 0 && typeof bidPrice === 'number') {
      closestBidPriceLevel = bidPrice;
    }
  }

  // Sum ask volume within TICKS_WITHIN_PRICE levels from the best ask
  for (let i = 0; i < Math.min(orderBook.asks.length, TICKS_WITHIN_PRICE); i++) {
    const askPrice = orderBook.asks[i][0];
    const askAmount = orderBook.asks[i][1];
    if (typeof askAmount === 'number') {
      totalAskVolumeNearPrice += askAmount;
    }
    if (i === 0 && typeof askPrice === 'number') {
      closestAskPriceLevel = askPrice;
    }
  }

  if (totalAskVolumeNearPrice === 0 && totalBidVolumeNearPrice === 0) {
      return { type: 'none', ratio: 1, nearPriceLevel: currentPrice };
  }


  if (totalAskVolumeNearPrice === 0) { // Avoid division by zero if no asks
    return { type: 'bid', ratio: Infinity, nearPriceLevel: closestBidPriceLevel };
  }
  if (totalBidVolumeNearPrice === 0) { // Avoid division by zero if no bids
    return { type: 'ask', ratio: Infinity, nearPriceLevel: closestAskPriceLevel };
  }

  const bidToAskRatio = totalBidVolumeNearPrice / totalAskVolumeNearPrice;
  const askToBidRatio = totalAskVolumeNearPrice / totalBidVolumeNearPrice;

  if (bidToAskRatio >= IMBALANCE_RATIO_THRESHOLD) {
    return { type: 'bid', ratio: bidToAskRatio, nearPriceLevel: closestBidPriceLevel };
  } else if (askToBidRatio >= IMBALANCE_RATIO_THRESHOLD) {
    return { type: 'ask', ratio: askToBidRatio, nearPriceLevel: closestAskPriceLevel };
  }

  return { type: 'none', ratio: 1, nearPriceLevel: currentPrice };
}

/**
 * Identifies key support and resistance levels from OHLCV data.
 * Simplified: uses recent high/low.
 * @param ohlcvData Array of OHLCV candles.
 * @returns Object containing identified support and resistance levels.
 */
export function identifyKeyLevels(ohlcvData: OHLCV[] | null): { support: number | null, resistance: number | null } {
  if (!ohlcvData || ohlcvData.length === 0) {
    return { support: null, resistance: null };
  }
  // Simplified: use min low as support, max high as resistance from the recent candles
  let minLow = Infinity;
  let maxHigh = -Infinity;
  ohlcvData.forEach(candle => {
    const lowPrice = candle[3];
    const highPrice = candle[2];
    if (typeof lowPrice === 'number' && lowPrice < minLow) {
      minLow = lowPrice;
    }
    if (typeof highPrice === 'number' && highPrice > maxHigh) {
      maxHigh = highPrice;
    }
  });
  return {
    support: minLow === Infinity ? null : minLow,
    resistance: maxHigh === -Infinity ? null : maxHigh
  };
}

/**
 * Analyzes CVD data to determine its trend.
 * @param cvdData Array of CVD data points.
 * @returns 'rising', 'falling', or 'flat'.
 */
export function analyzeCVDTrend(cvdData: { timestamp: number; cvd: number }[] | null): 'rising' | 'falling' | 'flat' {
  if (!cvdData || cvdData.length < 2) {
    return 'flat'; // Not enough data to determine trend
  }
  // Simplified: check if the latest CVD is higher/lower than an earlier point (e.g., 5 periods ago)
  const lookbackPeriods = Math.min(5, cvdData.length -1);
  const currentCvd = cvdData[cvdData.length - 1].cvd;
  const pastCvd = cvdData[cvdData.length - 1 - lookbackPeriods].cvd;

  if (currentCvd > pastCvd) return 'rising';
  if (currentCvd < pastCvd) return 'falling';
  return 'flat';
}

/**
 * Detects basic bullish/bearish candlestick patterns from the latest candle.
 * @param ohlcvData Array of OHLCV candles.
 * @returns A string indicating the pattern or 'none'.
 */
export function detectCandlestickPattern(ohlcvData: OHLCV[] | null): TradeSignal['candlestickPattern'] {
    if (!ohlcvData || ohlcvData.length === 0) {
        return 'none';
    }
    const latestCandle = ohlcvData[ohlcvData.length - 1];
    if (latestCandle.length < 5) return 'none'; // Ensure OHLCV structure

    const open = latestCandle[1];
    const high = latestCandle[2];
    const low = latestCandle[3];
    const close = latestCandle[4];

    if (typeof open !== 'number' || typeof high !== 'number' || typeof low !== 'number' || typeof close !== 'number') {
        return 'none'; // Essential values are not numbers
    }

    const bodySize = Math.abs(close - open);
    const upperShadow = high - Math.max(open, close);
    const lowerShadow = Math.min(open, close) - low;
    const range = high - low;

    if (range === 0) return 'none'; // Avoid division by zero for doji-like candles with no range

    // Simplified Pin Bar detection
    // Bullish Pin Bar: Small body, long lower shadow (at least 2x body), short upper shadow.
    if (lowerShadow >= 2 * bodySize && upperShadow < bodySize && close > open) {
        return 'bullish_pin_bar';
    }
    // Bearish Pin Bar: Small body, long upper shadow (at least 2x body), short lower shadow.
    if (upperShadow >= 2 * bodySize && lowerShadow < bodySize && close < open) {
        return 'bearish_pin_bar';
    }

    // Simplified Engulfing pattern detection (needs previous candle)
    if (ohlcvData.length < 2) return 'none';
    const prevCandle = ohlcvData[ohlcvData.length - 2];
    if (prevCandle.length < 5) return 'none'; // Ensure OHLCV structure for prev candle

    const prevOpen = prevCandle[1];
    const prevClose = prevCandle[4];

    if (typeof prevOpen !== 'number' || typeof prevClose !== 'number') {
        return 'none'; // Essential values for previous candle are not numbers
    }

    // Bullish Engulfing: Current green candle engulfs previous red candle's body.
    if (close > open && prevClose < prevOpen && close > prevOpen && open < prevClose) {
        return 'bullish_engulfing';
    }
    // Bearish Engulfing: Current red candle engulfs previous green candle's body.
    if (close < open && prevClose > prevOpen && close < prevOpen && open > prevClose) {
        return 'bearish_engulfing';
    }

    return 'none';
}


/**
 * Generates trade signals based on market data and indicators.
 * @param ohlcvData Array of OHLCV candles.
 * @param orderBook Current order book.
 * @param cvdData Array of CVD data points.
 * @param cvdEmaData Array of CVD EMA values.
 * @returns A TradeSignal object.
 */
export function generateSignals(
  ohlcvData: OHLCV[] | null,
  orderBook: OrderBook | null,
  cvdData: { timestamp: number; cvd: number }[] | null,
  // cvdEmaData: number[] | null // cvdEmaData currently not used in basic signal logic
): TradeSignal {
  let determinedCurrentPrice: number | undefined;
  let determinedTimestamp: number = Date.now();

  if (ohlcvData && ohlcvData.length > 0) {
    const lastCandleClose = ohlcvData[ohlcvData.length - 1][4];
    if (typeof lastCandleClose === 'number') {
      determinedCurrentPrice = lastCandleClose;
    }
    const lastCandleTimestamp = ohlcvData[ohlcvData.length - 1][0];
    if (typeof lastCandleTimestamp === 'number') {
      determinedTimestamp = lastCandleTimestamp;
    }
  }

  if (typeof determinedCurrentPrice !== 'number') { // If not found from OHLCV
    const bestBid = orderBook?.bids?.[0]?.[0];
    const bestAsk = orderBook?.asks?.[0]?.[0];
    if (typeof bestBid === 'number') {
      determinedCurrentPrice = bestBid;
    } else if (typeof bestAsk === 'number') {
      determinedCurrentPrice = bestAsk;
    }
  }

  if (typeof determinedCurrentPrice !== 'number' || determinedCurrentPrice <= 0) {
    return { type: 'hold', reason: 'Could not determine current market price.', timestamp: determinedTimestamp };
  }

  const currentPrice = determinedCurrentPrice; // Now a confirmed positive number
  const timestamp = determinedTimestamp;

  const { type: imbalanceType, ratio: imbalanceRatio, nearPriceLevel: imbalancePriceLevel } = detectOrderBookImbalance(orderBook, currentPrice);
  const { support, resistance } = identifyKeyLevels(ohlcvData);
  const cvdTrend = analyzeCVDTrend(cvdData);
  const candlestickPattern = detectCandlestickPattern(ohlcvData);

  // Long Entry Conditions
  if (
    imbalanceType === 'bid' &&
    typeof support === 'number' && typeof imbalancePriceLevel === 'number' && // Ensure levels are numbers
    Math.abs(imbalancePriceLevel - support) < (currentPrice * 0.01) && // Imbalance near support (1% tolerance)
    cvdTrend === 'rising' &&
    (candlestickPattern === 'bullish_pin_bar' || candlestickPattern === 'bullish_engulfing')
  ) {
    return {
      type: 'long',
      reason: 'Bid imbalance near support, rising CVD, bullish candlestick.',
      timestamp,
      priceAtSignal: currentPrice,
      keyLevel: support,
      imbalanceRatio,
      cvdDirection: cvdTrend,
      candlestickPattern
    };
  }

  // Short Entry Conditions
  if (
    imbalanceType === 'ask' &&
    typeof resistance === 'number' && typeof imbalancePriceLevel === 'number' && // Ensure levels are numbers
    Math.abs(imbalancePriceLevel - resistance) < (currentPrice * 0.01) && // Imbalance near resistance (1% tolerance)
    cvdTrend === 'falling' &&
    (candlestickPattern === 'bearish_pin_bar' || candlestickPattern === 'bearish_engulfing')
  ) {
    return {
      type: 'short',
      reason: 'Ask imbalance near resistance, falling CVD, bearish candlestick.',
      timestamp,
      priceAtSignal: currentPrice,
      keyLevel: resistance,
      imbalanceRatio,
      cvdDirection: cvdTrend,
      candlestickPattern
    };
  }

  return { type: 'hold', reason: 'No strong signal detected.', timestamp };
}

console.log('SignalGenerationAgent logic loaded.');
