import { StructuredToolInterface } from "@langchain/core/tools";
import { BaseMessage } from "@langchain/core/messages";
import { RunnableToolLike } from "@langchain/core/runnables";
import { ToolExecutor } from "./tool_executor.js";
import { CompiledStateGraph } from "../graph/state.js";
import { START } from "../constants.js";
/** @deprecated Use {@link createReactAgent} instead with tool calling. */
export type FunctionCallingExecutorState = {
    messages: Array<BaseMessage>;
};
/** @deprecated Use {@link createReactAgent} instead with tool calling. */
export declare function createFunctionCallingExecutor<Model extends object>({ model, tools, }: {
    model: Model;
    tools: Array<StructuredToolInterface | RunnableToolLike> | ToolExecutor;
}): CompiledStateGraph<FunctionCallingExecutorState, Partial<FunctionCallingExecutorState>, typeof START | "agent" | "action">;
