"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LastValue = void 0;
const errors_js_1 = require("../errors.cjs");
const base_js_1 = require("./base.cjs");
/**
 * Stores the last value received, can receive at most one value per step.
 *
 * Since `update` is only called once per step and value can only be of length 1,
 * LastValue always stores the last value of a single node. If multiple nodes attempt to
 * write to this channel in a single step, an error will be thrown.
 * @internal
 */
class LastValue extends base_js_1.BaseChannel {
    constructor() {
        super(...arguments);
        Object.defineProperty(this, "lc_graph_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "LastValue"
        });
        // value is an array so we don't misinterpret an update to undefined as no write
        Object.defineProperty(this, "value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
    }
    fromCheckpoint(checkpoint) {
        const empty = new LastValue();
        if (checkpoint) {
            empty.value = [checkpoint];
        }
        return empty;
    }
    update(values) {
        if (values.length === 0) {
            return false;
        }
        if (values.length !== 1) {
            throw new errors_js_1.InvalidUpdateError("LastValue can only receive one value per step.", {
                lc_error_code: "INVALID_CONCURRENT_GRAPH_UPDATE",
            });
        }
        // eslint-disable-next-line prefer-destructuring
        this.value = [values[values.length - 1]];
        return true;
    }
    get() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
    checkpoint() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
}
exports.LastValue = LastValue;
//# sourceMappingURL=last_value.js.map