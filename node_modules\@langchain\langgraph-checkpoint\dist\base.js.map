{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../src/base.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAMhC,OAAO,EACL,KAAK,EACL,SAAS,EACT,MAAM,EACN,SAAS,GAGV,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAiDzD,MAAM,UAAU,QAAQ,CAAI,GAAM;IAChC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;QAC3C,OAAO,GAAG,CAAC;KACZ;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE5C,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;QACrB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;YACjD,MAAuC,CAAC,GAAG,CAAC,GAAG,QAAQ,CACrD,GAA+B,CAAC,GAAG,CAAC,CACtC,CAAC;SACH;KACF;IAED,OAAO,MAAW,CAAC;AACrB,CAAC;AAED,cAAc;AACd,MAAM,UAAU,eAAe;IAC7B,OAAO;QACL,CAAC,EAAE,CAAC;QACJ,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC5B,cAAc,EAAE,EAAE;QAClB,gBAAgB,EAAE,EAAE;QACpB,aAAa,EAAE,EAAE;QACjB,aAAa,EAAE,EAAE;KAClB,CAAC;AACJ,CAAC;AAED,cAAc;AACd,MAAM,UAAU,cAAc,CAAC,UAA8B;IAC3D,OAAO;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;QACf,EAAE,EAAE,UAAU,CAAC,EAAE;QACjB,EAAE,EAAE,UAAU,CAAC,EAAE;QACjB,cAAc,EAAE,EAAE,GAAG,UAAU,CAAC,cAAc,EAAE;QAChD,gBAAgB,EAAE,EAAE,GAAG,UAAU,CAAC,gBAAgB,EAAE;QACpD,aAAa,EAAE,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC;QACjD,aAAa,EAAE,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC;KAC7C,CAAC;AACJ,CAAC;AAiBD,MAAM,OAAgB,mBAAmB;IAGvC,YAAY,KAA0B;QAFtC;;;;mBAA4B,IAAI,kBAAkB,EAAE;WAAC;QAGnD,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,MAAsB;QAC9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;IAC9C,CAAC;IA2BD;;;;;OAKG;IACH,cAAc,CAAC,OAAsB,EAAE,QAAyB;QAC9D,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;SACxE;QACD,OAAO,CACL,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAClE,CAAC;IACT,CAAC;CACF;AAED,MAAM,UAAU,sBAAsB,CACpC,CAAiB,EACjB,CAAiB;IAEjB,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QAClD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACzB;IAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,GAAG,QAA0B;IAE7B,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;QAC3C,IAAI,GAAG,KAAK,CAAC;YAAE,OAAO,OAAO,CAAC;QAC9B,OAAO,sBAAsB,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,cAAc,GAA2B;IACpD,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACX,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;CACb,CAAC;AAEF,MAAM,UAAU,eAAe,CAAC,MAAsB;IACpD,OAAO,CACL,MAAM,CAAC,YAAY,EAAE,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE,CAC3E,CAAC;AACJ,CAAC"}