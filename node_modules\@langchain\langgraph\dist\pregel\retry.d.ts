import { PregelExecutableTask } from "./types.js";
import { type RetryPolicy } from "./utils/index.js";
export declare const DEFAULT_INITIAL_INTERVAL = 500;
export declare const DEFAULT_BACKOFF_FACTOR = 2;
export declare const DEFAULT_MAX_INTERVAL = 128000;
export declare const DEFAULT_MAX_RETRIES = 3;
export type SettledPregelTask = {
    task: PregelExecutableTask<any, any>;
    error: Error;
    signalAborted?: boolean;
};
export declare function _runWithRetry<N extends PropertyKey, C extends PropertyKey>(pregelTask: PregelExecutableTask<N, C>, retryPolicy?: RetryPolicy, configurable?: Record<string, unknown>, signal?: AbortSignal): Promise<{
    task: PregelExecutableTask<N, C>;
    result: unknown;
    error: Error | undefined;
    signalAborted?: boolean;
}>;
