{"version": 3, "file": "messages.js", "sourceRoot": "", "sources": ["../../src/pregel/messages.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,mBAAmB,GAGpB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EACL,cAAc,EAEd,aAAa,EACb,kBAAkB,EAClB,aAAa,GACd,MAAM,0BAA0B,CAAC;AASlC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAM3D,SAAS,qBAAqB,CAAC,CAAU;IACvC,OAAO,aAAa,CAAE,CAAyB,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,sDAAsD;AACtD,8EAA8E;AAC9E,MAAM,OAAO,qBAAsB,SAAQ,mBAAmB;IAe5D,YAAY,QAA4C;QACtD,KAAK,EAAE,CAAC;QAfV;;;;mBAAO,uBAAuB;WAAC;QAE/B;;;;;WAA6C;QAE7C;;;;mBAAkC,EAAE;WAAC;QAErC;;;;mBAAoC,EAAE;WAAC;QAEvC;;;;mBAAkD,EAAE;WAAC;QAErD;;;;mBAA6C,EAAE;WAAC;QAEhD;;;;mBAAsB,IAAI;WAAC;QAIzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,IAAU,EAAE,OAAoB,EAAE,KAAa,EAAE,MAAM,GAAG,KAAK;QACnE,IACE,MAAM;YACN,OAAO,CAAC,EAAE,KAAK,SAAS;YACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,SAAS,EACnC;YACA,OAAO;SACR;QAED,IAAI,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;QAC3B,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YAC1B,6CAA6C;YAC7C,SAAS,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,YAAY,EAAE,CAAC;SAC3D;aAAM;YACL,mEAAmE;YACnE,yEAAyE;YACzE,oEAAoE;YACpE,+DAA+D;YAC/D,IAAI,SAAS,IAAI,IAAI,IAAI,SAAS,KAAK,OAAO,KAAK,EAAE,EAAE;gBACrD,SAAS;oBACP,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,OAAO,KAAK,EAAE,CAAC;aACjE;YAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,SAAS,CAAC;SAC9C;QAED,IAAI,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE;YAC5B,6CAA6C;YAC7C,OAAO,CAAC,EAAE,GAAG,SAAS,CAAC;YAEvB,6CAA6C;YAC7C,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC;SAClC;QAED,IAAI,OAAO,CAAC,EAAE,IAAI,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;QACxD,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,oBAAoB,CAClB,IAAgB,EAChB,SAA0B,EAC1B,KAAa,EACb,YAAqB,EACrB,YAAsC,EACtC,IAAe,EACf,QAAkC,EAClC,IAAa;QAEb,IACE,QAAQ;YACR,mCAAmC;YACnC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EACvE;YACA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;gBACrB,QAAQ,CAAC,uBAAkC,CAAC,KAAK,CAAC,GAAG,CAAC;gBACvD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,QAAQ,EAAE;aAC5B,CAAC;SACH;IACH,CAAC;IAED,iBAAiB,CACf,KAAa,EACb,IAAqB,EACrB,KAAa,EACb,YAAqB,EACrB,KAAgB,EAChB,MAAwC;QAExC,MAAM,KAAK,GAAG,MAAM,EAAE,KAAK,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;YACvC,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;gBAChC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACzD;iBAAM;gBACL,IAAI,CAAC,KAAK,CACR,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EACrB,IAAI,cAAc,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EACtC,KAAK,CACN,CAAC;aACH;SACF;IACH,CAAC;IAED,YAAY,CAAC,MAAiB,EAAE,KAAa;QAC3C,6EAA6E;QAC7E,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE;YACvC,MAAM,cAAc,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAmB,CAAC;YACtE,IAAI,aAAa,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE;gBAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACzE;YACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,8DAA8D;IAC9D,cAAc,CAAC,IAAS,EAAE,KAAa;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB,CACd,MAAkB,EAClB,MAAmB,EACnB,KAAa,EACb,YAAqB,EACrB,IAAe,EACf,QAAkC,EAClC,QAAiB,EACjB,IAAa;QAEb,IACE,QAAQ,KAAK,SAAS;YACtB,IAAI,KAAK,QAAQ,CAAC,cAAc;YAChC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAClD;YACA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;gBACrB,QAAQ,CAAC,uBAAkC,CAAC,KAAK,CAAC,GAAG,CAAC;gBACvD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,QAAQ,EAAE;aAC5B,CAAC;YAEF,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC9B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBACzC,IACE,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;wBACnD,KAAK,CAAC,EAAE,KAAK,SAAS,EACtB;wBACA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;qBAC7B;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;4BACxB,IACE,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;gCACjD,IAAI,CAAC,EAAE,KAAK,SAAS,EACrB;gCACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;6BAC3B;yBACF;qBACF;iBACF;aACF;SACF;IACH,CAAC;IAED,cAAc,CAAC,OAAoB,EAAE,KAAa;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC1B,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC5C;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACjC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;oBAC3B,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;wBACxB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;qBAC1C;iBACF;aACF;iBAAM,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;gBACzD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBAC1C,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;wBACxB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;qBAC1C;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;4BACxB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;gCACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;6BACzC;yBACF;qBACF;iBACF;aACF;SACF;IACH,CAAC;IAED,8DAA8D;IAC9D,gBAAgB,CAAC,IAAS,EAAE,KAAa;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;CACF"}