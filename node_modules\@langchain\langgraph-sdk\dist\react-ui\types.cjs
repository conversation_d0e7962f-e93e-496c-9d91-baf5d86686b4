"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uiMessageReducer = exports.isRemoveUIMessage = exports.isUIMessage = void 0;
function isUIMessage(message) {
    if (typeof message !== "object" || message == null)
        return false;
    if (!("type" in message))
        return false;
    return message.type === "ui";
}
exports.isUIMessage = isUIMessage;
function isRemoveUIMessage(message) {
    if (typeof message !== "object" || message == null)
        return false;
    if (!("type" in message))
        return false;
    return message.type === "remove-ui";
}
exports.isRemoveUIMessage = isRemoveUIMessage;
function uiMessageReducer(state, update) {
    const events = Array.isArray(update) ? update : [update];
    let newState = state.slice();
    for (const event of events) {
        if (event.type === "remove-ui") {
            newState = newState.filter((ui) => ui.id !== event.id);
            continue;
        }
        const index = state.findIndex((ui) => ui.id === event.id);
        if (index !== -1) {
            newState[index] = event.metadata.merge
                ? { ...event, props: { ...state[index].props, ...event.props } }
                : event;
        }
        else {
            newState.push(event);
        }
    }
    return newState;
}
exports.uiMessageReducer = uiMessageReducer;
