import { coerceMessageLikeToMessage, } from "@langchain/core/messages";
import { v4 } from "uuid";
import { StateGraph } from "./state.js";
/**
 * Prebuilt reducer that combines returned messages.
 * Can handle standard messages and special modifiers like {@link RemoveMessage}
 * instances.
 */
export function messagesStateReducer(left, right) {
    const leftArray = Array.isArray(left) ? left : [left];
    const rightArray = Array.isArray(right) ? right : [right];
    // coerce to message
    const leftMessages = leftArray.map(coerceMessageLikeToMessage);
    const rightMessages = rightArray.map(coerceMessageLikeToMessage);
    // assign missing ids
    for (const m of leftMessages) {
        if (m.id === null || m.id === undefined) {
            m.id = v4();
            m.lc_kwargs.id = m.id;
        }
    }
    for (const m of rightMessages) {
        if (m.id === null || m.id === undefined) {
            m.id = v4();
            m.lc_kwargs.id = m.id;
        }
    }
    // merge
    const merged = [...leftMessages];
    const mergedById = new Map(merged.map((m, i) => [m.id, i]));
    const idsToRemove = new Set();
    for (const m of rightMessages) {
        const existingIdx = mergedById.get(m.id);
        if (existingIdx !== undefined) {
            if (m._getType() === "remove") {
                idsToRemove.add(m.id);
            }
            else {
                idsToRemove.delete(m.id);
                merged[existingIdx] = m;
            }
        }
        else {
            if (m._getType() === "remove") {
                throw new Error(`Attempting to delete a message with an ID that doesn't exist ('${m.id}')`);
            }
            mergedById.set(m.id, merged.length);
            merged.push(m);
        }
    }
    return merged.filter((m) => !idsToRemove.has(m.id));
}
/** @ignore */
export class MessageGraph extends StateGraph {
    constructor() {
        super({
            channels: {
                __root__: {
                    reducer: messagesStateReducer,
                    default: () => [],
                },
            },
        });
    }
}
//# sourceMappingURL=message.js.map