{"version": 3, "file": "dynamic_barrier_value.js", "sourceRoot": "", "sources": ["../../src/channels/dynamic_barrier_value.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AACrE,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAMxD,SAAS,cAAc,CACrB,CAA8B;IAE9B,OAAQ,CAAyB,CAAC,OAAO,KAAK,SAAS,CAAC;AAC1D,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,OAAO,mBAA2B,SAAQ,WAI/C;IAOC;QACE,KAAK,EAAE,CAAC;QAPV;;;;mBAAgB,qBAAqB;WAAC;QAEtC;;;;;WAAmB,CAAC,2CAA2C;QAE/D;;;;;WAAiB;QAIf,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAS,CAAC;IAC/B,CAAC;IAED,cAAc,CAAC,UAA2C;QACxD,MAAM,KAAK,GAAG,IAAI,mBAAmB,EAAS,CAAC;QAC/C,IAAI,UAAU,EAAE;YACd,KAAK,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;QACD,OAAO,KAAa,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,MAAuC;QAC5C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,MAAM,IAAI,kBAAkB,CAC1B,0DAA0D,CAC3D,CAAC;aACH;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;SACb;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YACnC,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;oBACzB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;iBACH;gBACD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;wBACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACrB,OAAO,GAAG,IAAI,CAAC;qBAChB;iBACF;qBAAM;oBACL,MAAM,IAAI,kBAAkB,CAC1B,SAAS,KAAK,WAAW,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAC3C,CAAC;iBACH;aACF;YACD,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;YAClE,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAS,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACvB,OAAO,IAAI,CAAC;SACb;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,kEAAkE;IAClE,wCAAwC;IACxC,GAAG;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;YACvD,MAAM,IAAI,iBAAiB,EAAE,CAAC;SAC/B;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,UAAU;QACR,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpE,CAAC;CACF"}