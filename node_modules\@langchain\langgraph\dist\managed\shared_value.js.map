{"version": 3, "file": "shared_value.js", "sourceRoot": "", "sources": ["../../src/managed/shared_value.ts"], "names": [], "mappings": "AAAA,uDAAuD;AAIvD,OAAO,EACL,qBAAqB,EAIrB,oBAAoB,GACrB,MAAM,WAAW,CAAC;AACnB,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAWlD,MAAM,OAAO,WAAY,SAAQ,oBAAmC;IASlE,YAAY,MAA+B,EAAE,MAAyB;QACpE,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QATxB;;;;;WAAc;QAEd;;;;;WAAwB;QAExB;;;;;WAA2C;QAE3C;;;;mBAAe,EAAE;WAAC;QAIhB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;SAChB;aAAM,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,MAAM,iBAAiB,GACrB,OAAO,UAAU,KAAK,QAAQ;gBAC5B,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;SACjE;aAAM;YACL,MAAM,IAAI,KAAK,CACb,mBAAmB,IAAI,CAAC,KAAK,iEAAiE,CAC/F,CAAC;SACH;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,CACrB,MAAsB,EACtB,IAAuB;QAEvB,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxC,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC;QAC3B,OAAO,QAA0C,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,KAAa;QACrB,OAAO;YACL,GAAG,EAAE,WAAW;YAChB,MAAM,EAAE;gBACN,KAAK;gBACL,GAAG,EAAE,qBAAqB;aAC3B;SACF,CAAC;IACJ,CAAC;IAED,IAAI,CAAC,KAAa;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEO,aAAa,CAAC,MAAgB;QACpC,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,KAAK,MAAM,EAAE,IAAI,MAAM,EAAE;YACvB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACvC,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;wBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACrB,IAAI,IAAI,CAAC,EAAE,EAAE;4BACX,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;yBAC1D;qBACF;iBACF;qBAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;oBAC9C,MAAM,IAAI,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;iBAC7D;qBAAM;oBACL,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBAClB,IAAI,IAAI,CAAC,EAAE,EAAE;wBACX,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;qBACvD;iBACF;aACF;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;SAC5B;aAAM;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;SACpD;IACH,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,EAAE;YACzB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBACtC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAyB,CAAC,CAAC;SAC/B;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF"}