{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../src/pregel/validate.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAGvC,MAAM,OAAO,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,OAAgB;QAC1B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACrC,CAAC;CACF;AAED,MAAM,UAAU,aAAa,CAG3B,EACA,KAAK,EACL,QAAQ,EACR,aAAa,EACb,cAAc,EACd,cAAc,EACd,mBAAmB,EACnB,oBAAoB,GASrB;IACC,IAAI,CAAC,QAAQ,EAAE;QACb,MAAM,IAAI,oBAAoB,CAAC,uBAAuB,CAAC,CAAC;KACzD;IAED,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAY,CAAC;IAC/C,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAY,CAAC;IAE9C,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAChD,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,MAAM,IAAI,oBAAoB,CAAC,cAAc,SAAS,eAAe,CAAC,CAAC;SACxE;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE;YACnC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;SACrE;aAAM;YACL,MAAM,IAAI,oBAAoB,CAC5B,qBAAqB,OAAO,IAAI,uBAAuB,CACxD,CAAC;SACH;KACF;IAED,+BAA+B;IAC/B,KAAK,MAAM,IAAI,IAAI,kBAAkB,EAAE;QACrC,IAAI,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE;YACvB,MAAM,IAAI,oBAAoB,CAC5B,sBAAsB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CACtD,CAAC;SACH;KACF;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACjC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;YAC1C,MAAM,IAAI,oBAAoB,CAC5B,iBAAiB,MAAM,CACrB,aAAa,CACd,mCAAmC,CACrC,CAAC;SACH;KACF;SAAM;QACL,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE;YACtE,MAAM,IAAI,oBAAoB,CAC5B,8BAA8B,aAAa,gCAAgC,CAC5E,CAAC;SACH;KACF;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QAClC,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;KACvC;SAAM;QACL,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;KAC/D;IAED,IAAI,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACpD,iBAAiB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;KACvC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACxC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;KAC/D;IAED,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE;QACpC,IAAI,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE;YACvB,MAAM,IAAI,oBAAoB,CAC5B,mBAAmB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CACnD,CAAC;SACH;KACF;IAED,kCAAkC;IAClC,IAAI,mBAAmB,IAAI,mBAAmB,KAAK,GAAG,EAAE;QACtD,KAAK,MAAM,IAAI,IAAI,mBAAmB,EAAE;YACtC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;gBACpB,MAAM,IAAI,oBAAoB,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACrE;SACF;KACF;IAED,IAAI,oBAAoB,IAAI,oBAAoB,KAAK,GAAG,EAAE;QACxD,KAAK,MAAM,IAAI,IAAI,oBAAoB,EAAE;YACvC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE;gBACpB,MAAM,IAAI,oBAAoB,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACrE;SACF;KACF;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAE1B,IAAgC,EAAE,QAAY;IAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACvB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC,EAAE;gBACtB,MAAM,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;aAC7D;SACF;KACF;SAAM;QACL,IAAI,CAAC,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SAC9D;KACF;AACH,CAAC"}