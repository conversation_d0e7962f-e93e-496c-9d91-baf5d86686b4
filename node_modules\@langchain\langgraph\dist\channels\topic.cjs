"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Topic = void 0;
const errors_js_1 = require("../errors.cjs");
const base_js_1 = require("./base.cjs");
function arraysEqual(a, b) {
    return a.length === b.length && a.every((val, index) => val === b[index]);
}
/**
 * @internal
 */
class Topic extends base_js_1.BaseChannel {
    constructor(fields) {
        super();
        Object.defineProperty(this, "lc_graph_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "Topic"
        });
        Object.defineProperty(this, "unique", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "accumulate", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "seen", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "values", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.unique = fields?.unique ?? this.unique;
        this.accumulate = fields?.accumulate ?? this.accumulate;
        // State
        this.seen = new Set();
        this.values = [];
    }
    fromCheckpoint(checkpoint) {
        const empty = new Topic({
            unique: this.unique,
            accumulate: this.accumulate,
        });
        if (checkpoint) {
            empty.seen = new Set(checkpoint[0]);
            // eslint-disable-next-line prefer-destructuring
            empty.values = checkpoint[1];
        }
        return empty;
    }
    update(values) {
        const current = [...this.values];
        if (!this.accumulate) {
            this.values = [];
        }
        const flatValues = values.flat();
        if (flatValues.length > 0) {
            if (this.unique) {
                for (const value of flatValues) {
                    if (!this.seen.has(value)) {
                        this.seen.add(value);
                        this.values.push(value);
                    }
                }
            }
            else {
                this.values.push(...flatValues);
            }
        }
        return !arraysEqual(this.values, current);
    }
    get() {
        if (this.values.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.values;
    }
    checkpoint() {
        return [[...this.seen], this.values];
    }
}
exports.Topic = Topic;
//# sourceMappingURL=topic.js.map