"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.combineAbortSignals = exports.patchCheckpointMap = exports.patchConfigurable = exports._coerceToDict = exports.getNewChannelVersions = exports.getNullChannelVersion = void 0;
const constants_js_1 = require("../../constants.cjs");
function getNullChannelVersion(currentVersions) {
    const versionValues = Object.values(currentVersions);
    const versionType = versionValues.length > 0 ? typeof versionValues[0] : undefined;
    let nullVersion;
    if (versionType === "number") {
        nullVersion = 0;
    }
    else if (versionType === "string") {
        nullVersion = "";
    }
    return nullVersion;
}
exports.getNullChannelVersion = getNullChannelVersion;
function getNewChannelVersions(previousVersions, currentVersions) {
    // Get new channel versions
    if (Object.keys(previousVersions).length > 0) {
        const nullVersion = getNullChannelVersion(currentVersions);
        return Object.fromEntries(Object.entries(currentVersions).filter(([k, v]) => v > (previousVersions[k] ?? nullVersion)));
    }
    else {
        return currentVersions;
    }
}
exports.getNewChannelVersions = getNewChannelVersions;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function _coerceToDict(value, defaultKey) {
    return value &&
        !Array.isArray(value) &&
        // eslint-disable-next-line no-instanceof/no-instanceof
        !(value instanceof Date) &&
        typeof value === "object"
        ? value
        : { [defaultKey]: value };
}
exports._coerceToDict = _coerceToDict;
function patchConfigurable(config, 
// eslint-disable-next-line @typescript-eslint/no-explicit-any
patch) {
    if (config === null) {
        return { configurable: patch };
    }
    else if (config?.configurable === undefined) {
        return { ...config, configurable: patch };
    }
    else {
        return {
            ...config,
            configurable: { ...config.configurable, ...patch },
        };
    }
}
exports.patchConfigurable = patchConfigurable;
function patchCheckpointMap(config, metadata) {
    const parents = metadata?.parents ?? {};
    if (Object.keys(parents).length > 0) {
        return patchConfigurable(config, {
            [constants_js_1.CONFIG_KEY_CHECKPOINT_MAP]: {
                ...parents,
                [config.configurable?.checkpoint_ns ?? ""]: config.configurable?.checkpoint_id,
            },
        });
    }
    else {
        return config;
    }
}
exports.patchCheckpointMap = patchCheckpointMap;
/**
 * Combine multiple abort signals into a single abort signal.
 * @param signals - The abort signals to combine.
 * @returns A single abort signal that is aborted if any of the input signals are aborted.
 */
function combineAbortSignals(...signals) {
    if (signals.length === 1) {
        return signals[0];
    }
    if ("any" in AbortSignal) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return AbortSignal.any(signals);
    }
    const combinedController = new AbortController();
    const listener = () => {
        combinedController.abort();
        signals.forEach((s) => s.removeEventListener("abort", listener));
    };
    signals.forEach((s) => s.addEventListener("abort", listener));
    if (signals.some((s) => s.aborted)) {
        combinedController.abort();
    }
    return combinedController.signal;
}
exports.combineAbortSignals = combineAbortSignals;
//# sourceMappingURL=index.js.map