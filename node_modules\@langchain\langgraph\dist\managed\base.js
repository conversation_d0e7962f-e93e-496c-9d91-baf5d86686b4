import { RUNTIME_PLACEHOLDER } from "../constants.js";
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class ManagedValue {
    constructor(config, _params) {
        Object.defineProperty(this, "runtime", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_promises", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "lg_is_managed_value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        this.config = config;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    static async initialize(_config, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _args) {
        throw new Error("Not implemented");
    }
    async promises() {
        return Promise.all(this._promises);
    }
    addPromise(promise) {
        this._promises.push(promise);
    }
}
export class WritableManagedValue extends ManagedValue {
}
export const ChannelKeyPlaceholder = "__channel_key_placeholder__";
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export class ManagedValueMapping extends Map {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(entries) {
        super(entries ? Array.from(entries) : undefined);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    replaceRuntimeValues(step, values) {
        if (this.size === 0 || !values) {
            return;
        }
        if (Array.from(this.values()).every((mv) => !mv.runtime)) {
            return;
        }
        if (typeof values === "object" && !Array.isArray(values)) {
            for (const [key, value] of Object.entries(values)) {
                for (const [chan, mv] of this.entries()) {
                    if (mv.runtime && mv.call(step) === value) {
                        // eslint-disable-next-line no-param-reassign
                        values[key] = { [RUNTIME_PLACEHOLDER]: chan };
                    }
                }
            }
        }
        else if (typeof values === "object" && "constructor" in values) {
            for (const key of Object.getOwnPropertyNames(Object.getPrototypeOf(values))) {
                try {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = values[key];
                    for (const [chan, mv] of this.entries()) {
                        if (mv.runtime && mv.call(step) === value) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any, no-param-reassign
                            values[key] = { [RUNTIME_PLACEHOLDER]: chan };
                        }
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                }
                catch (error) {
                    // Ignore if TypeError
                    if (error.name !== TypeError.name) {
                        throw error;
                    }
                }
            }
        }
    }
    replaceRuntimePlaceholders(step, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    values) {
        if (this.size === 0 || !values) {
            return;
        }
        if (Array.from(this.values()).every((mv) => !mv.runtime)) {
            return;
        }
        if (typeof values === "object" && !Array.isArray(values)) {
            for (const [key, value] of Object.entries(values)) {
                if (typeof value === "object" &&
                    value !== null &&
                    RUNTIME_PLACEHOLDER in value) {
                    const placeholder = value[RUNTIME_PLACEHOLDER];
                    if (typeof placeholder === "string") {
                        // eslint-disable-next-line no-param-reassign
                        values[key] = this.get(placeholder)?.call(step);
                    }
                }
            }
        }
        else if (typeof values === "object" && "constructor" in values) {
            for (const key of Object.getOwnPropertyNames(Object.getPrototypeOf(values))) {
                try {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = values[key];
                    if (typeof value === "object" &&
                        value !== null &&
                        RUNTIME_PLACEHOLDER in value) {
                        const managedValue = this.get(value[RUNTIME_PLACEHOLDER]);
                        if (managedValue) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any, no-param-reassign
                            values[key] = managedValue.call(step);
                        }
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                }
                catch (error) {
                    // Ignore if TypeError
                    if (error.name !== TypeError.name) {
                        throw error;
                    }
                }
            }
        }
    }
}
export function isManagedValue(value) {
    if (typeof value === "object" && value && "lg_is_managed_value" in value) {
        return true;
    }
    return false;
}
export function isConfiguredManagedValue(value) {
    if (typeof value === "object" &&
        value &&
        "cls" in value &&
        "params" in value) {
        return true;
    }
    return false;
}
/**
 * No-op class used when getting state values, as managed values should never be returned
 * in get state calls.
 */
export class NoopManagedValue extends ManagedValue {
    call() { }
    static async initialize(config, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _args) {
        return Promise.resolve(new NoopManagedValue(config));
    }
}
//# sourceMappingURL=base.js.map