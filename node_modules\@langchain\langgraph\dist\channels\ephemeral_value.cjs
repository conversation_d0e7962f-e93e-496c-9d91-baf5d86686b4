"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EphemeralValue = void 0;
const errors_js_1 = require("../errors.cjs");
const index_js_1 = require("./index.cjs");
/**
 * Stores the value received in the step immediately preceding, clears after.
 * @internal
 */
class EphemeralValue extends index_js_1.BaseChannel {
    constructor(guard = true) {
        super();
        Object.defineProperty(this, "lc_graph_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "EphemeralValue"
        });
        Object.defineProperty(this, "guard", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        // value is an array so we don't misinterpret an update to undefined as no write
        Object.defineProperty(this, "value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        this.guard = guard;
    }
    fromCheckpoint(checkpoint) {
        const empty = new EphemeralValue(this.guard);
        if (checkpoint) {
            empty.value = [checkpoint];
        }
        return empty;
    }
    update(values) {
        if (values.length === 0) {
            const updated = this.value.length > 0;
            // If there are no updates for this specific channel at the end of the step, wipe it.
            this.value = [];
            return updated;
        }
        if (values.length !== 1 && this.guard) {
            throw new errors_js_1.InvalidUpdateError("EphemeralValue can only receive one value per step.");
        }
        // eslint-disable-next-line prefer-destructuring
        this.value = [values[values.length - 1]];
        return true;
    }
    get() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
    checkpoint() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
}
exports.EphemeralValue = EphemeralValue;
//# sourceMappingURL=ephemeral_value.js.map