import { Runnable, RunnableConfig, RunnableLike } from "@langchain/core/runnables";
import { Graph as DrawableGraph } from "@langchain/core/runnables/graph";
import { All, BaseCheckpointSaver } from "@langchain/langgraph-checkpoint";
import { PregelNode } from "../pregel/read.js";
import { Pregel } from "../pregel/index.js";
import type { PregelParams } from "../pregel/types.js";
import { BaseChannel } from "../channels/base.js";
import { END, Send, START } from "../constants.js";
import { RunnableCallable } from "../utils.js";
import { StateDefinition, StateType } from "./annotation.js";
import type { LangGraphRunnableConfig } from "../pregel/runnable_types.js";
export interface BranchOptions<IO, N extends string, CallOptions extends LangGraphRunnableConfig = LangGraphRunnableConfig> {
    source: N;
    path: RunnableLike<IO, BranchPathReturnValue, CallOptions>;
    pathMap?: Record<string, N | typeof END> | (N | typeof END)[];
}
export type BranchPathReturnValue = string | Send | (string | Send)[] | Promise<string | Send | (string | Send)[]>;
export declare class Branch<IO, N extends string, CallOptions extends LangGraphRunnableConfig = LangGraphRunnableConfig> {
    condition: Runnable<IO, BranchPathReturnValue, CallOptions>;
    ends?: Record<string, N | typeof END>;
    constructor(options: Omit<BranchOptions<IO, N, CallOptions>, "source">);
    run(writer: (dests: (string | Send)[], config: LangGraphRunnableConfig) => Runnable | void | Promise<void>, reader?: (config: CallOptions) => IO): RunnableCallable<unknown, unknown>;
    _route(input: IO, config: CallOptions, writer: (dests: (string | Send)[], config: LangGraphRunnableConfig) => Runnable | void | Promise<void>, reader?: (config: CallOptions) => IO): Promise<Runnable | any>;
}
export type NodeSpec<RunInput, RunOutput> = {
    runnable: Runnable<RunInput, RunOutput>;
    metadata?: Record<string, unknown>;
    subgraphs?: Pregel<any, any>[];
    ends?: string[];
};
export type AddNodeOptions = {
    metadata?: Record<string, unknown>;
    subgraphs?: Pregel<any, any>[];
    ends?: string[];
};
export declare class Graph<N extends string = typeof START | typeof END, RunInput = any, RunOutput = any, NodeSpecType extends NodeSpec<RunInput, RunOutput> = NodeSpec<RunInput, RunOutput>, C extends StateDefinition = StateDefinition> {
    nodes: Record<N, NodeSpecType>;
    edges: Set<[N | typeof START, N | typeof END]>;
    branches: Record<string, Record<string, Branch<RunInput, N, any>>>;
    entryPoint?: string;
    compiled: boolean;
    constructor();
    protected warnIfCompiled(message: string): void;
    get allEdges(): Set<[string, string]>;
    addNode<K extends string, NodeInput = RunInput>(key: K, action: RunnableLike<NodeInput, RunOutput extends object ? RunOutput & Record<string, any> : RunOutput, LangGraphRunnableConfig<StateType<C>>>, options?: AddNodeOptions): Graph<N | K, RunInput, RunOutput>;
    addEdge(startKey: N | typeof START, endKey: N | typeof END): this;
    addConditionalEdges(source: BranchOptions<RunInput, N, LangGraphRunnableConfig<StateType<C>>>): this;
    addConditionalEdges(source: N, path: RunnableLike<RunInput, BranchPathReturnValue, LangGraphRunnableConfig<StateType<C>>>, pathMap?: BranchOptions<RunInput, N, LangGraphRunnableConfig<StateType<C>>>["pathMap"]): this;
    /**
     * @deprecated use `addEdge(START, key)` instead
     */
    setEntryPoint(key: N): this;
    /**
     * @deprecated use `addEdge(key, END)` instead
     */
    setFinishPoint(key: N): this;
    compile({ checkpointer, interruptBefore, interruptAfter, name, }?: {
        checkpointer?: BaseCheckpointSaver | false;
        interruptBefore?: N[] | All;
        interruptAfter?: N[] | All;
        name?: string;
    }): CompiledGraph<N>;
    validate(interrupt?: string[]): void;
}
export declare class CompiledGraph<N extends string, State = any, Update = any, ConfigurableFieldType extends Record<string, any> = Record<string, any>, InputType = any, OutputType = any> extends Pregel<Record<N | typeof START, PregelNode<State, Update>>, Record<N | typeof START | typeof END | string, BaseChannel>, ConfigurableFieldType & Record<string, any>, InputType, OutputType> {
    NodeType: N;
    RunInput: State;
    RunOutput: Update;
    builder: Graph<N, State, Update>;
    constructor({ builder, ...rest }: {
        builder: Graph<N, State, Update>;
    } & PregelParams<Record<N | typeof START, PregelNode<State, Update>>, Record<N | typeof START | typeof END | string, BaseChannel>>);
    attachNode(key: N, node: NodeSpec<State, Update>): void;
    attachEdge(start: N | typeof START, end: N | typeof END): void;
    attachBranch(start: N | typeof START, name: string, branch: Branch<State, N>): void;
    /**
     * Returns a drawable representation of the computation graph.
     */
    getGraphAsync(config?: RunnableConfig & {
        xray?: boolean | number;
    }): Promise<DrawableGraph>;
    /**
     * Returns a drawable representation of the computation graph.
     *
     * @deprecated Use getGraphAsync instead. The async method will be the default in the next minor core release.
     */
    getGraph(config?: RunnableConfig & {
        xray?: boolean | number;
    }): DrawableGraph;
}
