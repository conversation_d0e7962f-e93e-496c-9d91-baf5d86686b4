{"version": 3, "file": "loop.js", "sourceRoot": "", "sources": ["../../src/pregel/loop.ts"], "names": [], "mappings": "AAEA,OAAO,EAIL,cAAc,EACd,eAAe,EAMf,iBAAiB,EACjB,cAAc,GACf,MAAM,iCAAiC,CAAC;AAEzC,OAAO,EAEL,gBAAgB,EAChB,aAAa,GACd,MAAM,qBAAqB,CAAC;AAQ7B,OAAO,EACL,SAAS,EACT,8BAA8B,EAE9B,yBAAyB,EACzB,eAAe,EACf,mBAAmB,EACnB,iBAAiB,EACjB,KAAK,EACL,KAAK,EACL,SAAS,EACT,YAAY,EACZ,MAAM,EACN,UAAU,EACV,IAAI,EACJ,qBAAqB,EACrB,wBAAwB,GACzB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EACL,YAAY,EACZ,iBAAiB,EACjB,kBAAkB,EAClB,SAAS,EACT,eAAe,GAEhB,MAAM,WAAW,CAAC;AACnB,OAAO,EACL,cAAc,EACd,kBAAkB,EAClB,eAAe,GAChB,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,eAAe,EACf,YAAY,GACb,MAAM,SAAS,CAAC;AACjB,OAAO,EACL,eAAe,EACf,cAAc,EACd,gBAAgB,GACjB,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAC5E,OAAO,EACL,aAAa,EACb,kBAAkB,EAClB,mBAAmB,EACnB,cAAc,GACf,MAAM,YAAY,CAAC;AAIpB,OAAO,EAAE,8BAA8B,EAAe,MAAM,aAAa,CAAC;AAE1E,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC5C,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACpD,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAiD9B,SAAS,kBAAkB,CAAC,GAAG,OAAyC;IACtE,OAAO,IAAI,8BAA8B,CAAC;QACxC,aAAa,EAAE,CAAC,KAAkB,EAAE,EAAE;YACpC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;gBAC5B,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;oBAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;aACF;QACH,CAAC;QACD,KAAK,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KAC5D,CAAC,CAAC;AACL,CAAC;AAED,MAAM,OAAO,UAAU;IA6ErB,IAAI,UAAU;QACZ,MAAM,kBAAkB,GACtB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;QAC7D,MAAM,qBAAqB,GACzB,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,KAAK,SAAS,CAAC;QAChE,MAAM,gBAAgB,GACpB,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;QAC3E,MAAM,sBAAsB,GAC1B,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;QAClD,MAAM,sBAAsB,GAC1B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC;QACrD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,KAAK,cAAc,CAAC;QAEtD,OAAO,CACL,kBAAkB;YAClB,CAAC,gBAAgB;gBACf,sBAAsB;gBACtB,sBAAsB;gBACtB,eAAe,CAAC,CACnB,CAAC;IACJ,CAAC;IAED,YAAY,MAAwB;QAlGpC,8DAA8D;QAC9D;;;;;WAAgC;QAEhC,8DAA8D;QAC9D;;;;;WAAY;QAEZ;;;;;WAAgC;QAEhC;;;;;WAA6C;QAE7C;;;;;WAGY;QAEZ;;;;;WAAsC;QAEtC;;;;;WAA6B;QAE7B;;;;;WAAiC;QAEjC;;;;;WAA2C;QAE3C;;;;;WAAuC;QAEvC;;;;;WAAwC;QAExC;;;;mBAA8D,EAAE;WAAC;QAEjE;;;;;WAAsE;QAEtE;;;;;WAAa;QAEb;;;;;WAAuB;QAEvB;;;;;WAAwC;QAExC;;;;;WAAwC;QAExC;;;;;WAA4C;QAE5C;;;;;WAAiC;QAEjC;;;;;WAA2D;QAE3D;;;;mBAKqB,SAAS;WAAC;QAE/B,8DAA8D;QAC9D;;;;mBAAwD,EAAE;WAAC;QAE3D,8DAA8D;QAC9D;;;;;WAAuC;QAEvC;;;;mBAA2C,EAAE;WAAC;QAE9C;;;;;WAAkB;QAElB;;;;mBAA0D,OAAO,CAAC,OAAO,EAAE;WAAC;QAE5E;;;;;WAA0B;QAE1B;;;;;WAAqC;QAErC;;;;;WAA+B;QAE/B;;;;;WAAgC;QAEhC;;;;mBAAsD,EAAE;WAAC;QAEzD;;;;mBAAiB,KAAK;WAAC;QAyBrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;QACxC,oEAAoE;QACpE,+BAA+B;QAC/B,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CACrE,IAAI,CAAC,YAAY,CAClB,CAAC;SACH;aAAM;YACL,IAAI,CAAC,0BAA0B,GAAG,SAAS,CAAC;SAC7C;QACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACpD,IAAI,CAAC,0BAA0B,GAAG,MAAM,CAAC,0BAA0B,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,uBAAuB,CAAC;QAC9D,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QACtD,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAC5B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,MAAkC;QACxD,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAChC,IACE,MAAM,KAAK,SAAS;YACpB,MAAM,CAAC,YAAY,EAAE,CAAC,iBAAiB,CAAC,KAAK,SAAS,EACtD;YACA,MAAM,GAAG,kBAAkB,CACzB,MAAM,EACN,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACvC,CAAC;SACH;QACD,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY;YACvC,CAAC,CAAC,CAAC,CAAC,eAAe,IAAI,MAAM,CAAC,YAAY,CAAC;YAC3C,CAAC,CAAC,IAAI,CAAC;QAET,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,qBAAqB,CAEjD,CAAC;QAEd,IAAI,MAAM,CAAC,YAAY,IAAI,UAAU,EAAE;YACrC,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,EAAE;gBAClC,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE;oBACjC,CAAC,wBAAwB,CAAC,EAAE;wBAC1B,MAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC;wBAC7C,UAAU,CAAC,eAAe,CAAC,QAAQ,EAAE;qBACtC,CAAC,IAAI,CAAC,8BAA8B,CAAC;iBACvC,CAAC,CAAC;aACJ;YAED,UAAU,CAAC,eAAe,IAAI,CAAC,CAAC;SACjC;QAED,MAAM,QAAQ,GAAG,eAAe,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC;QAChE,IACE,CAAC,QAAQ;YACT,MAAM,CAAC,YAAY,EAAE,aAAa,KAAK,SAAS;YAChD,MAAM,CAAC,YAAY,EAAE,aAAa,KAAK,EAAE,EACzC;YACA,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE;gBACjC,aAAa,EAAE,EAAE;gBACjB,aAAa,EAAE,SAAS;aACzB,CAAC,CAAC;SACJ;QACD,IAAI,gBAAgB,GAAG,MAAM,CAAC;QAC9B,IACE,MAAM,CAAC,YAAY,EAAE,CAAC,yBAAyB,CAAC,KAAK,SAAS;YAC9D,MAAM,CAAC,YAAY,EAAE,CAAC,yBAAyB,CAAC,EAAE,CAChD,MAAM,CAAC,YAAY,EAAE,aAAa,CACnC,EACD;YACA,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,EAAE;gBAC3C,aAAa,EACX,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAC5C,MAAM,CAAC,YAAY,EAAE,aAAa,CACnC;aACJ,CAAC,CAAC;SACJ;QACD,MAAM,mBAAmB,GACvB,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,KAAK,CACvC,8BAA8B,CAC/B,IAAI,EAAE,CAAC;QAEV,MAAM,KAAK,GAAoB,CAAC,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,CACjE,gBAAgB,CACjB,CAAC,IAAI;YACJ,MAAM;YACN,UAAU,EAAE,eAAe,EAAE;YAC7B,QAAQ,EAAE;gBACR,MAAM,EAAE,OAAO;gBACf,IAAI,EAAE,CAAC,CAAC;gBACR,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,EAAE;aACZ;YACD,aAAa,EAAE,EAAE;SAClB,CAAC;QACF,gBAAgB,GAAG;YACjB,GAAG,MAAM;YACT,GAAG,KAAK,CAAC,MAAM;YACf,YAAY,EAAE;gBACZ,aAAa,EAAE,EAAE;gBACjB,GAAG,MAAM,CAAC,YAAY;gBACtB,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY;aAC7B;SACF,CAAC;QACF,MAAM,oBAAoB,GAAG,KAAK,CAAC,YAAY,CAAC;QAChD,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACpD,MAAM,kBAAkB,GAAG,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAwB,CAAC;QACvE,MAAM,uBAAuB,GAAG,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC;QAE1D,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAEhE,MAAM,IAAI,GAAG,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,cAAc,IAAI,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACtE,MAAM,0BAA0B,GAAG,EAAE,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAEtE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK;YACxB,CAAC,CAAC,IAAI,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC;YACrC,CAAC,CAAC,SAAS,CAAC;QACd,IAAI,KAAK,EAAE;YACT,sEAAsE;YACtE,KAAK,CAAC,KAAK,EAAE,CAAC;SACf;QACD,OAAO,IAAI,UAAU,CAAC;YACpB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM;YACN,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,UAAU;YACV,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;YACpB,mBAAmB;YACnB,QAAQ;YACR,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,QAAQ;YACR,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,aAAa;YACb,IAAI;YACJ,IAAI;YACJ,0BAA0B;YAC1B,uBAAuB;YACvB,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;YACnC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;YACnC,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,MAAM;YACN,KAAK;YACL,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,KAAK,EAAE,MAAM,CAAC,KAAK;SACpB,CAAC,CAAC;IACL,CAAC;IAES,6BAA6B,CAAC,KAKvC;QACC,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CACtE,GAAG,EAAE;YACH,OAAO,IAAI,CAAC,YAAY,EAAE,GAAG,CAC3B,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,UAAU,EAChB,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,WAAW,CAClB,CAAC;QACJ,CAAC,CACF,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IACnE,CAAC;IAED,8DAA8D;IACpD,KAAK,CAAC,mBAAmB,CAAC,GAAW,EAAE,MAAa;QAC5D,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,EAAE,IAAI,QAAQ,IAAI,EAAE,IAAI,OAAO,EAAE,CAAC,MAAM,KAAK,UAAU,EAAE;YAC3D,MAAO,EAA2B,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACnD;IACH,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,MAAc,EAAE,MAA8B;QACtD,IAAI,UAAU,GAAG,MAAM,CAAC;QACxB,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAO;SACR;QAED,0DAA0D;QAC1D,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,cAAc,CAAC,EAAE;YACtD,UAAU,GAAG,KAAK,CAAC,IAAI,CACrB,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CACnD,CAAC;SACH;QACD,cAAc;QACd,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,UAAU,EAAE;YAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAChD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CACrC,CAAC;YACF,IAAI,CAAC,IAAI,cAAc,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACrC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACpD;iBAAM;gBACL,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aACnD;SACF;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,EAAE,SAAS,CAClD;YACE,GAAG,IAAI,CAAC,gBAAgB;YACxB,YAAY,EAAE;gBACZ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY;gBACrC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE;gBAC5D,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;aAClC;SACF,EACD,UAAU,EACV,MAAM,CACP,CAAC;QACF,IAAI,eAAe,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACjD;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;SACxC;IACH,CAAC;IAED,aAAa,CAAC,MAAc,EAAE,MAA2B,EAAE,MAAM,GAAG,KAAK;QACvE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IACE,IAAI,CAAC,MAAM,KAAK,SAAS;gBACzB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,EAC7C;gBACA,OAAO;aACR;YACD,IACE,MAAM,CAAC,MAAM,GAAG,CAAC;gBACjB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK;gBACtB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAC1B;gBACA,IAAI,CAAC,KAAK,CACR,kBAAkB,CAChB,eAAe,CACb,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,EAC3D,SAAS,CACV,CACF,CACF,CAAC;aACH;YACD,IAAI,CAAC,MAAM,EAAE;gBACX,IAAI,CAAC,KAAK,CACR,kBAAkB,CAChB,eAAe,CACb,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EACjE,OAAO,CACR,CACF,CACF,CAAC;aACH;SACF;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,IAAI,CAAC,MAAyC;QAClD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACvC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;SACrB;QACD,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAC7B,MAAM,IAAI,KAAK,CACb,oEAAoE,IAAI,CAAC,MAAM,GAAG,CACnF,CAAC;SACH;QACD,IAAI,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACtD,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC9B;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;YACjC,MAAM,IAAI,cAAc,EAAE,CAAC;SAC5B;aAAM,IACL,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EACjE;YACA,mBAAmB;YACnB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAClE,0BAA0B;YAC1B,MAAM,kBAAkB,GAAG,YAAY,CACrC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EACzB,IAAI,CAAC,0BAA0B,CAChC,CAAC;YACF,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC9D,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;aAC7C;YACD,wBAAwB;YACxB,MAAM,YAAY,GAAG,MAAM,cAAc,CACvC,eAAe,CACb,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,EACvD,QAAQ,CACT,CACF,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YACzB,uBAAuB;YACvB,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;YAClC,MAAM,IAAI,CAAC,cAAc,CAAC;gBACxB,MAAM,EAAE,MAAM;gBACd,MAAM,EACJ,gBAAgB,CACd,IAAI,CAAC,UAAU,EACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAC7D,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI;aACzB,CAAC,CAAC;YACH,gDAAgD;YAChD,IACE,eAAe,CACb,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,cAAc,EACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAC1B,EACD;gBACA,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC;gBAChC,MAAM,IAAI,cAAc,EAAE,CAAC;aAC5B;YAED,sBAAsB;YACtB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,KAAK,SAAS,EAAE;gBACjE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,mBAAmB,CAAC,CAAC;aACxD;SACF;aAAM;YACL,OAAO,KAAK,CAAC;SACd;QACD,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;YACzB,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;YAC7B,OAAO,KAAK,CAAC;SACd;QAED,MAAM,SAAS,GAAG,iBAAiB,CACjC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,EACJ;YACE,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CACF,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAEvB,uBAAuB;QACvB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,KAAK,CACR,MAAM,cAAc,CAClB,eAAe,CACb,kBAAkB,CAChB,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,wCAAwC;YACvD,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,kBAAkB,EACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EACzB,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,oBAAoB,CAC1B,EACD,OAAO,CACR,CACF,CACF,CAAC;SACH;QAED,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,OAAO,KAAK,CAAC;SACd;QACD,+DAA+D;QAC/D,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE;YACjE,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBACtD,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,EAAE;oBAClD,SAAS;iBACV;gBACD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;gBACjE,IAAI,IAAI,EAAE;oBACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;YACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBAChD;aACF;SACF;QACD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;SACjC;QAED,iDAAiD;QACjD,IACE,eAAe,CACb,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,eAAe,EACpB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAC1B,EACD;YACA,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;YACjC,MAAM,IAAI,cAAc,EAAE,CAAC;SAC5B;QACD,uBAAuB;QACvB,MAAM,WAAW,GAAG,MAAM,cAAc,CACtC,eAAe,CACb,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EACnD,OAAO,CACR,CACF,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAAa;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;YACnC,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAC5D;QACD,IAAI,QAAQ,EAAE;YACZ,4DAA4D;YAC5D,IACE,IAAI,CAAC,KAAK,KAAK,SAAS;gBACxB,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAChE;gBACA,MAAM,kBAAkB,GAAG,YAAY,CACrC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EACzB,IAAI,CAAC,0BAA0B,CAChC,CAAC;gBACF,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;oBAC9D,MAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,KAAK,CACR,kBAAkB,CAChB,eAAe,CACb,eAAe,CACb,IAAI,CAAC,UAAU,EACf,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAClD,IAAI,CAAC,QAAQ,CACd,EACD,QAAQ,CACT,CACF,CACF,CAAC;aACH;YAED,uBAAuB;YACvB,IAAI,CAAC,KAAK,CAAC;gBACT;oBACE,SAAS;oBACT;wBACE,CAAC,SAAS,CAAC,EAAG,KAAwB,CAAC,UAAU;qBAClD;iBACF;aACF,CAAC,CAAC;SACJ;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,UAAU,CACR,IAA0C,EAC1C,QAAgB,EAChB,IAAW;QAEX,IACE,IAAI,CAAC,cAAc,EAAE,MAAM,GAAG,CAAC;YAC/B,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,EAC7D;YACA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5B,OAAO;SACR;QAED,MAAM,MAAM,GAAG,kBAAkB,CAC/B,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAa,EAC5D,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,MAAM,IAAI,EAAE,EACjB,IAAI,EACJ;YACE,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CACF,CAAC;QACF,IAAI,MAAM,EAAE;YACV,IACE,IAAI,CAAC,eAAe,EAAE,MAAM,GAAG,CAAC;gBAChC,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,MAAM,CAAC,CAAC,EAChE;gBACA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC9B,OAAO;aACR;YACD,IAAI,CAAC,KAAK,CACR,kBAAkB,CAChB,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAC7D,CACF,CAAC;YACF,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;aACrC;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;YAC/B,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;aAC5C;YACD,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAES,kBAAkB,CAAC,CAAS;QACpC,OAAO,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC/C,CAAC;IAES,KAAK,CAAC,MAAM,CAAC,SAA4B;QACjD;;;;WAIG;QAEH,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;QAErC,gCAAgC;QAChC,MAAM,UAAU,GAAG,YAAY,EAAE,CAC/B,qBAAqB,CACF,CAAC;QAEtB,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE;YACrD,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACjE;QAED,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC;YAC5C,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;aACxE;YAED,MAAM,MAAM,GAAsC,EAAE,CAAC;YAErD,0BAA0B;YAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,IAAI,UAAU,CACxC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,uBAAuB,CAC7B,EAAE;gBACD,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;oBAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;iBAClB;gBACD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;aAChC;YACD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpC,MAAM,IAAI,eAAe,CAAC,8BAA8B,CAAC,CAAC;aAC3D;YAED,cAAc;YACd,KAAK,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;aACzB;SACF;QAED,oBAAoB;QACpB,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,uBAAuB,IAAI,EAAE,CAAC;aACpD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAA2B,CAAC;QACpD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,YAAY,CACV,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb;gBACE;oBACE,IAAI,EAAE,KAAK;oBACX,MAAM,EAAE,UAAU;oBAClB,QAAQ,EAAE,EAAE;iBACb;aACF,EACD,IAAI,CAAC,0BAA0B,CAChC,CAAC;SACH;QACD,MAAM,qBAAqB,GACzB,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,UAAU,IAAI,qBAAqB,EAAE;YAC5C,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACpD,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;oBAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;oBAC9D,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG;wBACzC,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC;wBAC3C,CAAC,WAAW,CAAC,EAAE,OAAO;qBACvB,CAAC;iBACH;aACF;YACD,wBAAwB;YACxB,MAAM,YAAY,GAAG,MAAM,cAAc,CACvC,eAAe,CACb,eAAe,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,EACrD,QAAQ,CACT,CACF,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;SAC1B;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;SAC7B;aAAM,IAAI,qBAAqB,EAAE;YAChC,kFAAkF;YAClF,2DAA2D;YAC3D,qEAAqE;YACrE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;SACzB;aAAM;YACL,gCAAgC;YAChC,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,MAAM,YAAY,GAAG,iBAAiB,CACpC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CACpB,CAAC;gBACF,YAAY,CACV,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACZ,MAAM,CAAC,MAAM,CAAC,YAAY,CAAsB,CAAC,MAAM,CAAC;oBACvD;wBACE,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE,WAA6B;wBACrC,QAAQ,EAAE,EAAE;qBACb;iBACF,CAAC,EACF,IAAI,CAAC,0BAA0B,CAChC,CAAC;gBACF,wBAAwB;gBACxB,MAAM,IAAI,CAAC,cAAc,CAAC;oBACxB,MAAM,EAAE,OAAO;oBACf,MAAM,EAAE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC;iBACxC,CAAC,CAAC;gBAEH,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;aACzB;iBAAM,IAAI,CAAC,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,EAAE;gBACrE,MAAM,IAAI,eAAe,CACvB,gCAAgC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACrE,CAAC;aACH;iBAAM;gBACL,kBAAkB;gBAClB,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;aACzB;SACF;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3C,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC,UAAU;aACvC,CAAC,CAAC;SACJ;IACH,CAAC;IAES,KAAK,CAAC,MAA+B;QAC7C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;aACxD;SACF;IACH,CAAC;IAES,KAAK,CAAC,cAAc,CAC5B,aAA2D;QAE3D,cAAc;QACd,MAAM,QAAQ,GAAG;YACf,GAAG,aAAa;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,yBAAyB,CAAC,IAAI,EAAE;SACrE,CAAC;QACF,0BAA0B;QAC1B,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,wDAAwD;YACxD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,EAAE,YAAY;gBAC7D,EAAE,aAAa;gBACf,CAAC,CAAC,IAAI,CAAC,gBAAgB;gBACvB,CAAC,CAAC,SAAS,CAAC;YAEd,wBAAwB;YACxB,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;YACnC,iEAAiE;YACjE,oEAAoE;YACpE,qEAAqE;YACrE,wEAAwE;YACxE,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAChC,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,IAAI,CACV,CAAC;YACF,IAAI,CAAC,gBAAgB,GAAG;gBACtB,GAAG,IAAI,CAAC,gBAAgB;gBACxB,YAAY,EAAE;oBACZ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY;oBACrC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE;iBAC7D;aACF,CAAC;YACF,MAAM,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAChE,MAAM,WAAW,GAAG,qBAAqB,CACvC,IAAI,CAAC,0BAA0B,EAC/B,eAAe,CAChB,CAAC;YACF,IAAI,CAAC,0BAA0B,GAAG,eAAe,CAAC;YAClD,4BAA4B;YAC5B,iEAAiE;YACjE,sDAAsD;YACtD,KAAK,IAAI,CAAC,6BAA6B,CAAC;gBACtC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBACpC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC3C,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,kBAAkB,EAAE;gBACxC,WAAW;aACZ,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG;gBACtB,GAAG,IAAI,CAAC,gBAAgB;gBACxB,YAAY,EAAE;oBACZ,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY;oBACrC,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;iBAClC;aACF,CAAC;SACH;QACD,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;IACjB,CAAC;IAES,YAAY,CACpB,KAA2D;QAE3D,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACtD,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,EAAE;gBAClD,SAAS;aACV;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;YAC5D,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC1B;SACF;QACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;aAChD;SACF;IACH,CAAC;CACF"}