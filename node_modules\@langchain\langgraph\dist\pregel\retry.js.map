{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../src/pregel/retry.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAEhE,OAAO,EAAE,4BAA4B,EAAE,MAAM,mBAAmB,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAoB,MAAM,kBAAkB,CAAC;AAEvE,MAAM,CAAC,MAAM,wBAAwB,GAAG,GAAG,CAAC;AAC5C,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,CAAC;AACxC,MAAM,CAAC,MAAM,oBAAoB,GAAG,MAAM,CAAC;AAC3C,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAErC,MAAM,uBAAuB,GAAG;IAC9B,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG,EAAE,WAAW;CACjB,CAAC;AAEF,8DAA8D;AAC9D,MAAM,wBAAwB,GAAG,CAAC,KAAU,EAAE,EAAE;IAC9C,IACE,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC;QACtC,KAAK,CAAC,IAAI,KAAK,YAAY,EAC3B;QACA,OAAO,KAAK,CAAC;KACd;IACD,8DAA8D;IAC9D,IAAK,KAAa,EAAE,IAAI,KAAK,cAAc,EAAE;QAC3C,OAAO,KAAK,CAAC;KACd;IACD,MAAM,MAAM;IACV,8DAA8D;IAC7D,KAAa,EAAE,QAAQ,EAAE,MAAM,IAAK,KAAa,EAAE,MAAM,CAAC;IAC7D,IAAI,MAAM,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE;QACvD,OAAO,KAAK,CAAC;KACd;IACD,8DAA8D;IAC9D,IAAK,KAAa,EAAE,KAAK,EAAE,IAAI,KAAK,oBAAoB,EAAE;QACxD,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AASF,MAAM,CAAC,KAAK,UAAU,aAAa;AAIjC,8DAA8D;AAC9D,UAAsC,EACtC,WAAyB,EACzB,YAAsC,EACtC,MAAoB;IAOpB,MAAM,mBAAmB,GAAG,UAAU,CAAC,YAAY,IAAI,WAAW,CAAC;IACnE,IAAI,QAAQ,GACV,mBAAmB,KAAK,SAAS;QAC/B,CAAC,CAAC,mBAAmB,CAAC,eAAe,IAAI,wBAAwB;QACjE,CAAC,CAAC,CAAC,CAAC;IACR,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,KAAK,CAAC;IACV,IAAI,MAAM,CAAC;IAEX,IAAI,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;IAC5B,IAAI,YAAY,EAAE;QAChB,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;KAClD;IAED,MAAM,GAAG;QACP,GAAG,MAAM;QACT,MAAM;KACP,CAAC;IAEF,iDAAiD;IACjD,OAAO,IAAI,EAAE;QACX,IAAI,MAAM,EAAE,OAAO,EAAE;YACnB,gEAAgE;YAChE,wEAAwE;YACxE,MAAM;SACP;QACD,0CAA0C;QAC1C,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtD,KAAK,GAAG,SAAS,CAAC;QAClB,IAAI;YACF,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAChE,MAAM;SACP;QAAC,OAAO,CAAU,EAAE;YACnB,KAAK,GAAG,CAAC,CAAC;YACT,KAAkC,CAAC,YAAY,GAAG,UAAU,CAAC,EAAE,CAAC;YACjE,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM,EAAE,GAAW,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC;gBACvD,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC;gBAC1B,IAAI,GAAG,CAAC,KAAK,KAAK,EAAE,EAAE;oBACpB,mDAAmD;oBACnD,KAAK,MAAM,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE;wBACvC,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;qBAClC;oBACD,KAAK,GAAG,SAAS,CAAC;oBAClB,MAAM;iBACP;qBAAM,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE;oBACvC,gEAAgE;oBAChE,MAAM,QAAQ,GAAG,4BAA4B,CAAC,EAAE,CAAC,CAAC;oBAClD,KAAK,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC;wBAC1B,GAAG,KAAK,CAAC,OAAO;wBAChB,KAAK,EAAE,QAAQ;qBAChB,CAAC,CAAC;iBACJ;aACF;YACD,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM;aACP;YACD,IAAI,mBAAmB,KAAK,SAAS,EAAE;gBACrC,MAAM;aACP;YACD,QAAQ,IAAI,CAAC,CAAC;YACd,6BAA6B;YAC7B,IACE,QAAQ,IAAI,CAAC,mBAAmB,CAAC,WAAW,IAAI,mBAAmB,CAAC,EACpE;gBACA,MAAM;aACP;YACD,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,IAAI,wBAAwB,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACnB,MAAM;aACP;YACD,QAAQ,GAAG,IAAI,CAAC,GAAG,CACjB,mBAAmB,CAAC,WAAW,IAAI,oBAAoB,EACvD,QAAQ,GAAG,CAAC,mBAAmB,CAAC,aAAa,IAAI,sBAAsB,CAAC,CACzE,CAAC;YACF,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,MAAM;gBACnD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;gBAC7C,CAAC,CAAC,QAAQ,CAAC;YACb,wBAAwB;YACxB,sDAAsD;YACtD,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC;YACxE,gBAAgB;YAChB,MAAM,SAAS,GACZ,KAAe,CAAC,IAAI;gBACrB,8DAA8D;gBAC5D,KAAe,CAAC,WAAmB,CAAC,iBAAiB;gBACtD,KAAe,CAAC,WAAW,CAAC,IAAI,CAAC;YACpC,IAAI,mBAAmB,EAAE,UAAU,IAAI,IAAI,EAAE;gBAC3C,OAAO,CAAC,GAAG,CACT,kBAAkB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC,OAAO,CAClE,CAAC,CACF,eAAe,QAAQ,WAAW,SAAS,KAAK,KAAK,EAAE,CACzD,CAAC;aACH;YAED,4CAA4C;YAC5C,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;SACrE;KACF;IACD,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,MAAM;QACN,KAAK,EAAE,KAA0B;QACjC,aAAa,EAAE,MAAM,EAAE,OAAO;KAC/B,CAAC;AACJ,CAAC"}