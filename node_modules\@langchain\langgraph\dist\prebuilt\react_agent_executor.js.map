{"version": 3, "file": "react_agent_executor.js", "sourceRoot": "", "sources": ["../../src/prebuilt/react_agent_executor.ts"], "names": [], "mappings": "AAKA,OAAO,EAGL,WAAW,EACX,aAAa,EACb,aAAa,EACb,aAAa,GACd,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,QAAQ,EAGR,cAAc,EAEd,gBAAgB,EAChB,eAAe,GAChB,MAAM,2BAA2B,CAAC;AASnC,OAAO,EACL,UAAU,GAGX,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAE1C,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAY,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AACrE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAuB/C,SAAS,+BAA+B,CACtC,eAAgC;IAEhC,iCAAiC;IACjC,IACE,OAAO,eAAe,KAAK,QAAQ;QACnC,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,EAC3E;QACA,OAAO,eAAe,CAAC;KACxB;IAED,2BAA2B;IAC3B,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;QACzC,OAAO,KAAK,EAAE,KAAsC,EAAE,EAAE,CACtD,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KACnC;IAED,kBAAkB;IAClB,IAAI,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;QACxC,OAAO,cAAc,CAAC,IAAI,CACxB,CAAC,KAAsC,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAC3D,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACzB;IAED,MAAM,IAAI,KAAK,CACb,wCAAwC,OAAO,eAAe,EAAE,CACjE,CAAC;AACJ,CAAC;AAED,MAAM,oBAAoB,GAAG,QAAQ,CAAC;AAEtC,SAAS,kBAAkB,CAAC,MAAe;IACzC,IAAI,cAAiC,CAAC;IAEtC,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,cAAc,GAAG,cAAc,CAAC,IAAI,CAClC,CAAC,KAAsC,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAC3D,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;KACjD;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACrC,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;QAChD,cAAc,GAAG,cAAc,CAAC,IAAI,CAClC,CAAC,KAAsC,EAAE,EAAE;YACzC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC,CACF,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;KACjD;SAAM,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;QAClE,cAAc,GAAG,cAAc,CAAC,IAAI,CAClC,CAAC,KAAsC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,CACxE,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC,CAAC;KACjD;SAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;QACvC,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC;YACtD,OAAO,EAAE,oBAAoB;SAC9B,CAAC,CAAC;KACJ;SAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QACtC,cAAc,GAAG,MAAM,CAAC;KACzB;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,qCAAqC,OAAO,MAAM,EAAE,CAAC,CAAC;KACvE;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,UAAU,CACjB,MAAe,EACf,aAAuD,EACvD,eAA2D;IAE3D,oCAAoC;IACpC,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC,MAAM,CAClE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CACjB,CAAC,MAAM,CAAC;IACT,IAAI,YAAY,GAAG,CAAC,EAAE;QACpB,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;KACH;IAED,IAAI,WAAW,GAAG,MAAM,CAAC;IACzB,IAAI,aAAa,IAAI,IAAI,EAAE;QACzB,WAAW,GAAG,aAAa,CAAC;KAC7B;SAAM,IAAI,eAAe,IAAI,IAAI,EAAE;QAClC,WAAW,GAAG,+BAA+B,CAAC,eAAe,CAAC,CAAC;KAChE;IAED,OAAO,kBAAkB,CAAC,WAAW,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAwB;IAChD,OAAO,CACL,QAAQ,IAAI,KAAK;QACjB,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;QAClC,YAAY,IAAI,KAAK,CACtB,CAAC;AACJ,CAAC;AAQD,8DAA8D;AAC9D,SAAS,oBAAoB,CAAC,KAAU;IACtC,OAAO,CACL,yBAAyB,IAAI,KAAK;QAClC,QAAQ,IAAI,KAAK;QACjB,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CACnC,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,GAAsB,EACtB,KAAmE;IAEnE,uFAAuF;IACvF,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,IAAI,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;QAC9C,KAAK;YACH,KAAK,CAAC,KAAK,CAAC,IAAI,CACd,CAAC,IAAI,EAAE,EAAE,CACP,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACvC,gBAAgB,CAAC,IAAI,CAAC;gBACtB,oBAAoB,CAAC,IAAI,CAAC,CAC7B,IAAI,KAAK,CAAC;KACd;IAED,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC/B,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;KAC9B;IAED,iDAAiD;IACjD,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;QAC7C,OAAO,IAAI,CAAC;KACb;IAED,8CAA8C;IAC9C,IACE,CAAC,KAAK,CAAC,MAAM;QACb,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;QAChC,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,EAC1B;QACA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAyB,CAAC;IACxD,eAAe;IACf,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,sBAAsB,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;QACtE,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;KACjD;IAED,+BAA+B;IAC/B,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;QACtC,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;KACH;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;IAEzC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAClC,IAAI,aAAiC,CAAC;QAEtC,oBAAoB;QACpB,IAAI,MAAM,IAAI,SAAS,IAAI,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE;YACxD,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;SACzC;QACD,kCAAkC;aAC7B,IAAI,MAAM,IAAI,SAAS,EAAE;YAC5B,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;SAChC;QACD,qBAAqB;aAChB,IAAI,UAAU,IAAI,SAAS,IAAI,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE;YAChE,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;SACzC;QACD,uCAAuC;aAClC;YACH,SAAS;SACV;QAED,IAAI,aAAa,EAAE;YACjB,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;SACnC;KACF;IAED,MAAM,YAAY,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CACb,kBAAkB,YAAY,6BAA6B;YACzD,iFAAiF,CACpF,CAAC;KACH;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,GAAmD;IAEnD,uFAAuF;IACvF,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,IAAI,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;QAC9C,KAAK;YACH,KAAK,CAAC,KAAK,CAAC,IAAI,CACd,CAAC,IAAI,EAAE,EAAE,CACP,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBACvC,gBAAgB,CAAC,IAAI,CAAC;gBACtB,oBAAoB,CAAC,IAAI,CAAC,CAC7B,IAAI,KAAK,CAAC;KACd;IAED,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE;QAC/B,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;KAC9B;IAED,kDAAkD;IAClD,IAAI,eAAe,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;QAC5C,KAAK,GAAG,KAAK,CAAC,KAAsB,CAAC;KACtC;IAED,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CACb,8HAA8H,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CACvJ,CAAC;KACH;IAED,OAAO,KAAsB,CAAC;AAChC,CAAC;AA0BD,MAAM,CAAC,MAAM,0BAA0B,GAAG,GAGtC,EAAE,CACJ,UAAU,CAAC,IAAI,CAAC;IACd,QAAQ,EAAE,UAAU,CAA0B;QAC5C,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,GAAG,EAAE,CAAC,EAAE;KAClB,CAAC;IACF,kBAAkB,EAAE,CAAA,UAAa,CAAA;CAClC,CAAC,CAAC;AAoFL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyCG;AAEH,MAAM,UAAU,gBAAgB,CAM9B,MAA2D;IAY3D,MAAM,EACJ,GAAG,EACH,KAAK,EACL,eAAe,EACf,aAAa,EACb,MAAM,EACN,WAAW,EACX,eAAe,EACf,YAAY,EACZ,eAAe,EACf,cAAc,EACd,KAAK,EACL,cAAc,EACd,IAAI,EACJ,gBAAgB,GACjB,GAAG,MAAM,CAAC;IAEX,IAAI,WAAyE,CAAC;IAC9E,IAAI,QAAkB,CAAC;IACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACzB,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC;QAC1B,QAAQ,GAAG,KAAK,CAAC;KAClB;SAAM;QACL,WAAW,GAAG,KAAK,CAAC;QACpB,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;KAChC;IAED,IAAI,mBAAmB,GAAoB,IAAI,CAAC;IAEhD,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAsB,EAAE,EAAE;QACxD,IAAI,mBAAmB,EAAE;YACvB,OAAO,mBAAmB,CAAC;SAC5B;QAED,IAAI,cAAiC,CAAC;QACtC,IAAI,MAAM,gBAAgB,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,SAAS,KAAK,UAAU,EAAE;gBAChE,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,gCAAgC,CAAC,CAAC;aAC7D;YACD,cAAc,GAAG,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SAC7C;aAAM;YACL,cAAc,GAAG,GAAG,CAAC;SACtB;QAED,MAAM,cAAc,GAAG,UAAU,CAC/B,MAAM,EACN,aAAa,EACb,eAAe,CACJ,CAAC;QAEd,MAAM,aAAa,GACjB,gBAAgB,KAAK,QAAQ;YAC3B,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;YACtE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE1C,mBAAmB,GAAG,aAAa,CAAC;QACpC,OAAO,aAAa,CAAC;IACvB,CAAC,CAAC;IAEF,uEAAuE;IACvE,gDAAgD;IAChD,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAChC,WAAW;SACR,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;SAC7D,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5B,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,KAA2C,EAAE,EAAE;QACrE,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;QAC3B,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAClD,IACE,WAAW,CAAC,WAAW,CAAC;YACxB,CAAC,CAAC,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,EAChE;YACA,OAAO,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,GAAG,CAAC;SACtE;aAAM;YACL,OAAO,UAAU,CAAC;SACnB;IACH,CAAC,CAAC;IAEF,MAAM,0BAA0B,GAAG,KAAK,EACtC,KAA2C,EAC3C,MAAuB,EACvB,EAAE;QACF,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,qGAAqG,CACtG,CAAC;SACH;QACD,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,yBAAyB,CAAC;QAE9B,IACE,OAAO,cAAc,KAAK,QAAQ;YAClC,QAAQ,IAAI,cAAc;YAC1B,QAAQ,IAAI,cAAc,EAC1B;YACA,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAC1C,yBAAyB,GAAG,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB,CACrE,MAAM,CACP,CAAC;YACF,QAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;SAC1D;aAAM;YACL,yBAAyB,GAAG,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,oBAAoB,CACrE,cAAc,CACf,CAAC;SACH;QAED,MAAM,QAAQ,GAAG,MAAM,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC1E,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,KAAK,EACrB,KAA2C,EAC3C,MAAuB,EACvB,EAAE;QACF,2DAA2D;QAC3D,4DAA4D;QAC5D,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAClD,gCAAgC;QAChC,MAAM,QAAQ,GAAG,CAAC,MAAM,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAgB,CAAC;QAC5E,kCAAkC;QAClC,iEAAiE;QACjE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;QAC/B,OAAO,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;IAClC,CAAC,CAAC;IAEF,MAAM,QAAQ,GAAG,IAAI,UAAU,CAC7B,WAAW,IAAI,0BAA0B,EAA4B,CACtE;SACE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;SAC3B,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC1B,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAE3B,IAAI,cAAc,KAAK,SAAS,EAAE;QAChC,QAAQ;aACL,OAAO,CAAC,8BAA8B,EAAE,0BAA0B,CAAC;aACnE,OAAO,CAAC,8BAA8B,EAAE,GAAG,CAAC;aAC5C,mBAAmB,CAAC,OAAO,EAAE,cAAc,EAAE;YAC5C,QAAQ,EAAE,OAAO;YACjB,CAAC,GAAG,CAAC,EAAE,GAAG;YACV,4BAA4B,EAAE,8BAA8B;SAC7D,CAAC,CAAC;KACN;SAAM;QACL,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,EAAE;YACpD,QAAQ,EAAE,OAAO;YACjB,CAAC,GAAG,CAAC,EAAE,GAAG;SACX,CAAC,CAAC;KACJ;IAED,MAAM,kBAAkB,GAAG,CAAC,KAA2C,EAAE,EAAE;QACzE,wCAAwC;QACxC,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YACtD,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;gBAC3B,MAAM;aACP;YACD,sDAAsD;YACtD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,kBAAkB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACtE,OAAO,GAAG,CAAC;aACZ;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;IAEF,IAAI,kBAAkB,CAAC,IAAI,GAAG,CAAC,EAAE;QAC/B,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;KAC3E;SAAM;QACL,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACpC;IAED,OAAO,QAAQ,CAAC,OAAO,CAAC;QACtB,YAAY,EAAE,YAAY,IAAI,eAAe;QAC7C,eAAe;QACf,cAAc;QACd,KAAK;QACL,IAAI;KACL,CAAC,CAAC;AACL,CAAC"}