"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoopManagedValue = exports.isConfiguredManagedValue = exports.isManagedValue = exports.ManagedValueMapping = exports.ChannelKeyPlaceholder = exports.WritableManagedValue = exports.ManagedValue = void 0;
const constants_js_1 = require("../constants.cjs");
// eslint-disable-next-line @typescript-eslint/no-explicit-any
class ManagedValue {
    constructor(config, _params) {
        Object.defineProperty(this, "runtime", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: false
        });
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "_promises", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "lg_is_managed_value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
        this.config = config;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    static async initialize(_config, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _args) {
        throw new Error("Not implemented");
    }
    async promises() {
        return Promise.all(this._promises);
    }
    addPromise(promise) {
        this._promises.push(promise);
    }
}
exports.ManagedValue = ManagedValue;
class WritableManagedValue extends ManagedValue {
}
exports.WritableManagedValue = WritableManagedValue;
exports.ChannelKeyPlaceholder = "__channel_key_placeholder__";
// eslint-disable-next-line @typescript-eslint/no-explicit-any
class ManagedValueMapping extends Map {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(entries) {
        super(entries ? Array.from(entries) : undefined);
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    replaceRuntimeValues(step, values) {
        if (this.size === 0 || !values) {
            return;
        }
        if (Array.from(this.values()).every((mv) => !mv.runtime)) {
            return;
        }
        if (typeof values === "object" && !Array.isArray(values)) {
            for (const [key, value] of Object.entries(values)) {
                for (const [chan, mv] of this.entries()) {
                    if (mv.runtime && mv.call(step) === value) {
                        // eslint-disable-next-line no-param-reassign
                        values[key] = { [constants_js_1.RUNTIME_PLACEHOLDER]: chan };
                    }
                }
            }
        }
        else if (typeof values === "object" && "constructor" in values) {
            for (const key of Object.getOwnPropertyNames(Object.getPrototypeOf(values))) {
                try {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = values[key];
                    for (const [chan, mv] of this.entries()) {
                        if (mv.runtime && mv.call(step) === value) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any, no-param-reassign
                            values[key] = { [constants_js_1.RUNTIME_PLACEHOLDER]: chan };
                        }
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                }
                catch (error) {
                    // Ignore if TypeError
                    if (error.name !== TypeError.name) {
                        throw error;
                    }
                }
            }
        }
    }
    replaceRuntimePlaceholders(step, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    values) {
        if (this.size === 0 || !values) {
            return;
        }
        if (Array.from(this.values()).every((mv) => !mv.runtime)) {
            return;
        }
        if (typeof values === "object" && !Array.isArray(values)) {
            for (const [key, value] of Object.entries(values)) {
                if (typeof value === "object" &&
                    value !== null &&
                    constants_js_1.RUNTIME_PLACEHOLDER in value) {
                    const placeholder = value[constants_js_1.RUNTIME_PLACEHOLDER];
                    if (typeof placeholder === "string") {
                        // eslint-disable-next-line no-param-reassign
                        values[key] = this.get(placeholder)?.call(step);
                    }
                }
            }
        }
        else if (typeof values === "object" && "constructor" in values) {
            for (const key of Object.getOwnPropertyNames(Object.getPrototypeOf(values))) {
                try {
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    const value = values[key];
                    if (typeof value === "object" &&
                        value !== null &&
                        constants_js_1.RUNTIME_PLACEHOLDER in value) {
                        const managedValue = this.get(value[constants_js_1.RUNTIME_PLACEHOLDER]);
                        if (managedValue) {
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any, no-param-reassign
                            values[key] = managedValue.call(step);
                        }
                    }
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                }
                catch (error) {
                    // Ignore if TypeError
                    if (error.name !== TypeError.name) {
                        throw error;
                    }
                }
            }
        }
    }
}
exports.ManagedValueMapping = ManagedValueMapping;
function isManagedValue(value) {
    if (typeof value === "object" && value && "lg_is_managed_value" in value) {
        return true;
    }
    return false;
}
exports.isManagedValue = isManagedValue;
function isConfiguredManagedValue(value) {
    if (typeof value === "object" &&
        value &&
        "cls" in value &&
        "params" in value) {
        return true;
    }
    return false;
}
exports.isConfiguredManagedValue = isConfiguredManagedValue;
/**
 * No-op class used when getting state values, as managed values should never be returned
 * in get state calls.
 */
class NoopManagedValue extends ManagedValue {
    call() { }
    static async initialize(config, 
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _args) {
        return Promise.resolve(new NoopManagedValue(config));
    }
}
exports.NoopManagedValue = NoopManagedValue;
//# sourceMappingURL=base.js.map