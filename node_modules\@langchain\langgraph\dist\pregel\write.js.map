{"version": 3, "file": "write.js", "sourceRoot": "", "sources": ["../../src/pregel/write.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,QAAQ,GAGT,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,OAAO,EAAE,eAAe,EAAQ,KAAK,EAAE,MAAM,iBAAiB,CAAC;AACxE,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAIlD,MAAM,CAAC,MAAM,UAAU,GAAG;IACxB,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI;CACpC,CAAC;AAEF,SAAS,YAAY,CAAC,CAAU;IAC9B,OAAO,CACL,OAAO,CAAC,KAAK,QAAQ;QACrB,8DAA8D;QAC7D,CAAS,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,KAAK,SAAS,CACxD,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI;CACrC,CAAC;AAEF,SAAS,cAAc,CAAC,CAAU;IAChC,OAAO,CACL,OAAO,CAAC,KAAK,QAAQ;QACrB,8DAA8D;QAC7D,CAAS,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,KAAK,SAAS,CACzD,CAAC;AACJ,CAAC;AAED,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;AAEtC;;;GAGG;AACH,MAAM,OAAO,YAGX,SAAQ,gBAAoC;IAG5C,YACE,MAAgE,EAChE,IAAe;QAEf,MAAM,IAAI,GAAG,gBAAgB,MAAM;aAChC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACd,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;gBACnB,OAAO,MAAM,CAAC,IAAI,CAAC;aACpB;iBAAM,IAAI,SAAS,IAAI,MAAM,EAAE;gBAC9B,OAAO,MAAM,CAAC,OAAO,CAAC;aACvB;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAChB,KAAK,CAAC;YACJ,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE;YACzB,IAAI,EAAE,KAAK,EAAE,KAAe,EAAE,MAAuB,EAAE,EAAE;gBACvD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC;SACF,CAAC,CAAC;QArBL;;;;;WAAiE;QAuB/D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,KAAc,EAAE,MAAsB;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACvC,IAAI,yBAAyB,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACnE,OAAO;oBACL,MAAM,EAAE,KAAK,CAAC,MAAM;oBACpB,KAAK,EAAE,KAAK;iBACb,CAAC;aACH;iBAAM,IAAI,oBAAoB,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACrE,OAAO;oBACL,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC;aACH;iBAAM;gBACL,OAAO,KAAK,CAAC;aACd;QACH,CAAC,CAAC,CAAC;QACH,MAAM,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oCAAoC;IACpC,MAAM,CAAC,KAAK,CAAC,OAAO,CAClB,MAAsB,EACtB,MAA6D;QAE7D,WAAW;QACX,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACtB,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAAE;gBAC3B,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,EAAE;oBACvB,MAAM,IAAI,kBAAkB,CAC1B,4CAA4C,CAC7C,CAAC;iBACH;gBACD,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;oBAC3B,MAAM,IAAI,kBAAkB,CAAC,oCAAoC,CAAC,CAAC;iBACpE;aACF;YACD,IAAI,yBAAyB,CAAC,CAAC,CAAC,EAAE;gBAChC,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;oBAC3B,MAAM,IAAI,kBAAkB,CAAC,oCAAoC,CAAC,CAAC;iBACpE;aACF;SACF;QACD,8DAA8D;QAC9D,MAAM,YAAY,GAAoB,EAAE,CAAC;QACzC,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACtB,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACd,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;aAC/B;iBAAM,IAAI,yBAAyB,CAAC,CAAC,CAAC,EAAE;gBACvC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC5D,IAAI,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnD,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;iBACpC;aACF;iBAAM,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAAE;gBAClC,MAAM,WAAW,GACf,CAAC,CAAC,MAAM,KAAK,SAAS;oBACpB,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC;oBACxC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACd,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;oBAC7B,SAAS;iBACV;gBACD,IAAI,CAAC,CAAC,QAAQ,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC3C,SAAS;iBACV;gBACD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;aAC7C;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aAC9D;SACF;QACD,MAAM,KAAK,GAAc,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;QAChE,KAAK,CAAC,YAAY,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,QAAsB;QACpC,OAAO;QACL,uDAAuD;QACvD,QAAQ,YAAY,YAAY;YAChC,CAAC,SAAS,IAAI,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CACjD,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,cAAc,CAAqB,QAAW;QACnD,OAAO,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;IACrE,CAAC;CACF;AASD,SAAS,oBAAoB,CAAC,CAAU;IACtC,OAAO,CACL,CAAC,KAAK,SAAS,IAAI,OAAQ,CAAuB,CAAC,OAAO,KAAK,QAAQ,CACxE,CAAC;AACJ,CAAC;AAQD,SAAS,yBAAyB,CAAC,CAAU;IAC3C,OAAO,CACL,CAAC,KAAK,SAAS;QACf,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACxB,QAAQ,CAAC,UAAU,CAAE,CAA4B,CAAC,MAAM,CAAC,CAC1D,CAAC;AACJ,CAAC"}