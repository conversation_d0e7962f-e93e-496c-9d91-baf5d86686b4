{"version": 3, "file": "agentName.js", "sourceRoot": "", "sources": ["../../src/prebuilt/agentName.ts"], "names": [], "mappings": "AACA,OAAO,EACL,SAAS,EAGT,WAAW,EACX,gBAAgB,EAChB,aAAa,EACb,kBAAkB,GAEnB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAE7E,MAAM,YAAY,GAAG,sBAAsB,CAAC;AAC5C,MAAM,eAAe,GAAG,4BAA4B,CAAC;AAIrD;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,mBAAmB,CACjC,OAAU;IAEV,MAAM,IAAI,GACR,aAAa,CAAC,OAAO,CAAC;QACtB,CAAC,WAAW,CAAC,OAAO,CAAC;YACnB,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAEhE,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QAC1B,OAAO,OAAO,CAAC;KAChB;IAED,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAEzB,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;QACvC,OAAO,IAAI,SAAS,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;gBACjD,CAAC,CAAC,OAAO,CAAC,SAAS;gBACnB,CAAC,CAAC,OAAO,CAAC;YACZ,OAAO,EAAE,SAAS,IAAI,mBAAmB,OAAO,CAAC,OAAO,YAAY;YACpE,IAAI,EAAE,SAAS;SAChB,CAAC,CAAC;KACJ;IAED,MAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAI,cAAc,GAAG,CAAC,CAAC;IAEvB,KAAK,MAAM,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE;QAC1C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YACpC,cAAc,IAAI,CAAC,CAAC;YACpB,cAAc,CAAC,IAAI,CACjB,SAAS,IAAI,mBAAmB,YAAY,YAAY,CACzD,CAAC;SACH;aAAM,IACL,OAAO,YAAY,KAAK,QAAQ;YAChC,MAAM,IAAI,YAAY;YACtB,YAAY,CAAC,IAAI,KAAK,MAAM,EAC5B;YACA,cAAc,IAAI,CAAC,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC;gBAClB,GAAG,YAAY;gBACf,IAAI,EAAE,SAAS,IAAI,mBAAmB,YAAY,CAAC,IAAI,YAAY;aACpE,CAAC,CAAC;SACJ;aAAM;YACL,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;KACF;IAED,IAAI,CAAC,cAAc,EAAE;QACnB,cAAc,CAAC,OAAO,CAAC;YACrB,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,SAAS,IAAI,4BAA4B;SAChD,CAAC,CAAC;KACJ;IACD,OAAO,IAAI,SAAS,CAAC;QACnB,GAAG,OAAO,CAAC,SAAS;QACpB,OAAO,EAAE,cAAgC;QACzC,IAAI,EAAE,SAAS;KAChB,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,sBAAsB,CAAwB,OAAU;IACtE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAC7C,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,cAAc,GAAmB,EAAE,CAAC;IACxC,IAAI,WAA+B,CAAC;IAEpC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClC,cAAc,GAAG,OAAO,CAAC,OAAO;aAC7B,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAChB,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;gBACzB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBACvD,+FAA+F;gBAC/F,IAAI,SAAS,IAAI,CAAC,CAAC,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;oBAC1D,+BAA+B;oBAC/B,gDAAgD;oBAChD,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC3B,OAAO,KAAK,CAAC;iBACd;gBACD,OAAO,IAAI,CAAC;aACb;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;gBACzB,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAEvD,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE;oBAC/B,OAAO,KAAK,CAAC;iBACd;gBAED,+BAA+B;gBAC/B,gDAAgD;gBAChD,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBAE3B,OAAO;oBACL,GAAG,KAAK;oBACR,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;iBACtB,CAAC;aACH;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;KACN;SAAM;QACL,MAAM,OAAO,GAAG,OAAO,CAAC,OAAiB,CAAC;QAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEpD,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE;YAC/B,OAAO,OAAO,CAAC;SAChB;QAED,gDAAgD;QAChD,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3B,gDAAgD;QAChD,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;KAClC;IAED,OAAO,IAAI,SAAS,CAAC;QACnB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;YACjD,CAAC,CAAC,OAAO,CAAC,SAAS;YACnB,CAAC,CAAC,OAAO,CAAC;QACZ,OAAO,EAAE,cAAc;QACvB,IAAI,EAAE,WAAW;KAClB,CAAM,CAAC;AACV,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,aAAa,CAC3B,KAAwB,EACxB,aAA4B;IAE5B,IAAI,mBAAkE,CAAC;IACvE,IAAI,oBAA2D,CAAC;IAEhE,IAAI,aAAa,KAAK,QAAQ,EAAE;QAC9B,mBAAmB,GAAG,mBAAmB,CAAC;QAC1C,oBAAoB,GAAG,sBAAsB,CAAC;KAC/C;SAAM;QACL,MAAM,IAAI,KAAK,CACb,4BAA4B,aAAa,gCAAgC,CAC1E,CAAC;KACH;IAED,SAAS,oBAAoB,CAC3B,QAA2B;QAE3B,OAAO,QAAQ,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,gBAAgB,CAAC,IAAI,CAAC;QAC3B,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACzC,KAAK;QACL,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC;KAC1C,CAAC,CAAC;AACL,CAAC"}