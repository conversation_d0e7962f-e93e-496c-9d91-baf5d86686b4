"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const debug_js_1 = require("./debug.cjs");
const last_value_js_1 = require("../channels/last_value.cjs");
const errors_js_1 = require("../errors.cjs");
(0, globals_1.describe)("wrap", () => {
    (0, globals_1.it)("should wrap text with color codes", () => {
        const color = {
            start: "\x1b[34m",
            end: "\x1b[0m",
        };
        const text = "test text";
        const result = (0, debug_js_1.wrap)(color, text);
        (0, globals_1.expect)(result).toBe(`${color.start}${text}${color.end}`);
    });
});
(0, globals_1.describe)("_readChannels", () => {
    (0, globals_1.it)("should read values from channels", () => {
        const channels = {
            channel1: new last_value_js_1.LastValue(),
            channel2: new last_value_js_1.LastValue(),
        };
        // Update channels with values
        channels.channel1.update(["value1"]);
        channels.channel2.update(["42"]);
        const results = Array.from((0, debug_js_1._readChannels)(channels));
        (0, globals_1.expect)(results).toEqual([
            ["channel1", "value1"],
            ["channel2", "42"],
        ]);
    });
    (0, globals_1.it)("should skip empty channels", () => {
        const mockEmptyChannel = {
            lc_graph_name: "MockChannel",
            lg_is_channel: true,
            ValueType: "",
            UpdateType: [],
            get: globals_1.jest.fn().mockImplementation(() => {
                throw new errors_js_1.EmptyChannelError("Empty channel");
            }),
            update: globals_1.jest.fn().mockReturnValue(true),
            checkpoint: globals_1.jest.fn(),
            fromCheckpoint: globals_1.jest
                .fn()
                .mockReturnThis(),
            consume: globals_1.jest.fn().mockReturnValue(false),
        };
        const channels = {
            channel1: new last_value_js_1.LastValue(),
            emptyChannel: mockEmptyChannel,
        };
        // Update channel with value
        channels.channel1.update(["value1"]);
        const results = Array.from((0, debug_js_1._readChannels)(channels));
        (0, globals_1.expect)(results).toEqual([["channel1", "value1"]]);
    });
    (0, globals_1.it)("should propagate non-empty channel errors", () => {
        const mockErrorChannel = {
            lc_graph_name: "MockChannel",
            lg_is_channel: true,
            ValueType: "",
            UpdateType: [],
            get: globals_1.jest.fn().mockImplementation(() => {
                throw new Error("Other error");
            }),
            update: globals_1.jest.fn().mockReturnValue(true),
            checkpoint: globals_1.jest.fn(),
            fromCheckpoint: globals_1.jest
                .fn()
                .mockReturnThis(),
            consume: globals_1.jest.fn().mockReturnValue(false),
        };
        const channels = {
            channel1: new last_value_js_1.LastValue(),
            errorChannel: mockErrorChannel,
        };
        channels.channel1.update(["value1"]);
        (0, globals_1.expect)(() => Array.from((0, debug_js_1._readChannels)(channels))).toThrow("Other error");
    });
});
(0, globals_1.describe)("tasksWithWrites", () => {
    (0, globals_1.it)("should return task descriptions with no writes", () => {
        const tasks = [
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [],
            },
            {
                id: "task2",
                name: "Task 2",
                path: ["PULL", "Task 2"],
                interrupts: [],
            },
        ];
        const pendingWrites = [];
        const result = (0, debug_js_1.tasksWithWrites)(tasks, pendingWrites);
        (0, globals_1.expect)(result).toEqual([
            { id: "task1", name: "Task 1", path: ["PULL", "Task 1"], interrupts: [] },
            { id: "task2", name: "Task 2", path: ["PULL", "Task 2"], interrupts: [] },
        ]);
    });
    (0, globals_1.it)("should include error information", () => {
        const tasks = [
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [],
            },
            {
                id: "task2",
                name: "Task 2",
                path: ["PULL", "Task 2"],
                interrupts: [],
            },
        ];
        const pendingWrites = [
            ["task1", "__error__", { message: "Test error" }],
        ];
        const result = (0, debug_js_1.tasksWithWrites)(tasks, pendingWrites);
        (0, globals_1.expect)(result).toEqual([
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                error: { message: "Test error" },
                interrupts: [],
            },
            { id: "task2", name: "Task 2", path: ["PULL", "Task 2"], interrupts: [] },
        ]);
    });
    (0, globals_1.it)("should include state information", () => {
        const tasks = [
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [],
            },
            {
                id: "task2",
                name: "Task 2",
                path: ["PULL", "Task 2"],
                interrupts: [],
            },
        ];
        const pendingWrites = [];
        const states = {
            task1: { configurable: { key: "value" } },
        };
        const result = (0, debug_js_1.tasksWithWrites)(tasks, pendingWrites, states);
        (0, globals_1.expect)(result).toEqual([
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [],
                state: { configurable: { key: "value" } },
            },
            { id: "task2", name: "Task 2", path: ["PULL", "Task 2"], interrupts: [] },
        ]);
    });
    (0, globals_1.it)("should include interrupts", () => {
        const tasks = [
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [],
            },
        ];
        const pendingWrites = [
            ["task1", "__interrupt__", { value: "Interrupted", when: "during" }],
        ];
        const result = (0, debug_js_1.tasksWithWrites)(tasks, pendingWrites);
        (0, globals_1.expect)(result).toEqual([
            {
                id: "task1",
                name: "Task 1",
                path: ["PULL", "Task 1"],
                interrupts: [{ value: "Interrupted", when: "during" }],
            },
        ]);
    });
});
//# sourceMappingURL=debug.test.js.map