"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uuid5 = exports.uuid6 = void 0;
const uuid_1 = require("uuid");
function uuid6(clockseq) {
    return (0, uuid_1.v6)({ clockseq });
}
exports.uuid6 = uuid6;
// Skip UUID validation check, since UUID6s
// generated with negative clockseq are not
// technically compliant, but still work.
// See: https://github.com/uuidjs/uuid/issues/511
function uuid5(name, namespace) {
    const namespaceBytes = namespace
        .replace(/-/g, "")
        .match(/.{2}/g)
        .map((byte) => parseInt(byte, 16));
    return (0, uuid_1.v5)(name, new Uint8Array(namespaceBytes));
}
exports.uuid5 = uuid5;
//# sourceMappingURL=id.js.map