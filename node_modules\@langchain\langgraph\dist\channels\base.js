import { deepCopy, uuid6, } from "@langchain/langgraph-checkpoint";
import { EmptyChannelError } from "../errors.js";
export function isBaseChannel(obj) {
    return obj != null && obj.lg_is_channel === true;
}
export class BaseChannel {
    constructor() {
        Object.defineProperty(this, "ValueType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "UpdateType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /** @ignore */
        Object.defineProperty(this, "lg_is_channel", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
    }
    /**
     * Mark the current value of the channel as consumed. By default, no-op.
     * This is called by <PERSON><PERSON> before the start of the next step, for all
     * channels that triggered a node. If the channel was updated, return true.
     */
    consume() {
        return false;
    }
}
export function emptyChannels(channels, checkpoint) {
    const filteredChannels = Object.fromEntries(Object.entries(channels).filter(([, value]) => isBaseChannel(value)));
    const newChannels = {};
    for (const k in filteredChannels) {
        if (Object.prototype.hasOwnProperty.call(filteredChannels, k)) {
            const channelValue = checkpoint.channel_values[k];
            newChannels[k] = filteredChannels[k].fromCheckpoint(channelValue);
        }
    }
    return newChannels;
}
export function createCheckpoint(checkpoint, channels, step) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let values;
    if (channels === undefined) {
        values = checkpoint.channel_values;
    }
    else {
        values = {};
        for (const k of Object.keys(channels)) {
            try {
                values[k] = channels[k].checkpoint();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            }
            catch (error) {
                if (error.name === EmptyChannelError.unminifiable_name) {
                    // no-op
                }
                else {
                    throw error; // Rethrow unexpected errors
                }
            }
        }
    }
    return {
        v: 1,
        id: uuid6(step),
        ts: new Date().toISOString(),
        channel_values: values,
        channel_versions: { ...checkpoint.channel_versions },
        versions_seen: deepCopy(checkpoint.versions_seen),
        pending_sends: checkpoint.pending_sends ?? [],
    };
}
//# sourceMappingURL=base.js.map