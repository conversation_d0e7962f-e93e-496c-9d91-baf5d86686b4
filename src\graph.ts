import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { OrderBook, OHLCV, Trade } from 'ccxt';
import { fetchOHLCVData, fetchOrderBookData, fetchTradesData, initializeExchange } from './agents/dataCollectionAgent';
import { calculateCVD, fetchCVDEMA, calculateATR } from './agents/indicatorCalculationAgent';
import { generateSignals, TradeSignal } from './agents/signalGenerationAgent';
import { evaluateSignalWithGrok, TradeDecision } from './agents/decisionMakingAgent';
import { executeTrade, ExecutionResult } from './agents/orderExecutionAgent';

// Define the state structure for the graph
interface AppState {
  symbol: string;
  ohlcv?: OHLCV[] | null;
  orderBook?: OrderBook | null;
  trades?: Trade[] | null;
  cvd?: { timestamp: number; cvd: number }[] | null;
  cvdEma?: number[] | null;
  atr?: number | null; // Added ATR
  signal?: TradeSignal | null;
  decision?: TradeDecision | null;
  execution?: ExecutionResult | null; // Added for trade execution result
  error?: string | null;
}

// Correctly define the state schema using Annotation.Root
const appState = Annotation.Root({
  symbol: Annotation<string>(),
  ohlcv: Annotation<OHLCV[] | null | undefined>({
    value: (_current: OHLCV[] | null | undefined, update: OHLCV[] | null | undefined) => update,
    default: () => null
  }),
  orderBook: Annotation<OrderBook | null | undefined>({
    value: (_current: OrderBook | null | undefined, update: OrderBook | null | undefined) => update,
    default: () => null
  }),
  trades: Annotation<Trade[] | null | undefined>({
    value: (_current: Trade[] | null | undefined, update: Trade[] | null | undefined) => update,
    default: () => null
  }),
  cvd: Annotation<{ timestamp: number; cvd: number }[] | null | undefined>({
    value: (_current: { timestamp: number; cvd: number }[] | null | undefined, update: { timestamp: number; cvd: number }[] | null | undefined) => update,
    default: () => null
  }),
  cvdEma: Annotation<number[] | null | undefined>({
    value: (_current: number[] | null | undefined, update: number[] | null | undefined) => update,
    default: () => null
  }),
  atr: Annotation<number | null | undefined>({ // Added ATR
    value: (_current: number | null | undefined, update: number | null | undefined) => update,
    default: () => null
  }),
  signal: Annotation<TradeSignal | null | undefined>({
    value: (_current: TradeSignal | null | undefined, update: TradeSignal | null | undefined) => update,
    default: () => null
  }),
  decision: Annotation<TradeDecision | null | undefined>({
    value: (_current: TradeDecision | null | undefined, update: TradeDecision | null | undefined) => update,
    default: () => null
  }),
  execution: Annotation<ExecutionResult | null | undefined>({ // Added for trade execution result
    value: (_current: ExecutionResult | null | undefined, update: ExecutionResult | null | undefined) => update,
    default: () => null
  }),
  error: Annotation<string | null | undefined>({
    value: (_current: string | null | undefined, update: string | null | undefined) => update,
    default: () => null
  }),
});

// Define the DataCollectionNode
async function dataCollectionNode(state: AppState): Promise<Partial<AppState>> {
  console.log(`\n--- Executing Data Collection Node for ${state.symbol} ---`);
  try {
    // Initialize exchange first
    const initialized = await initializeExchange();
    if (!initialized) {
      return {
        error: 'Failed to initialize exchange connection',
        ohlcv: null,
        orderBook: null,
        trades: null
      };
    }

    const ohlcv = await fetchOHLCVData(state.symbol);
    const orderBook = await fetchOrderBookData(state.symbol);
    const trades = await fetchTradesData(state.symbol);

    return {
      ohlcv,
      orderBook,
      trades,
      error: null,
    };
  } catch (error: any) {
    console.error('Error in Data Collection Node:', error);
    return { error: error.message || 'Unknown error in data collection' };
  }
}

// Define the IndicatorCalculationNode
async function indicatorCalculationNode(state: AppState): Promise<Partial<AppState>> {
  console.log(`\n--- Executing Indicator Calculation Node for ${state.symbol} ---`);
  if (!state.trades || !state.ohlcv) {
    console.warn('Trades or OHLCV data is missing for indicator calculation.');
    return {
      cvd: null,
      cvdEma: null,
      atr: null,
      error: 'Trades or OHLCV data is missing for indicator calculation'
    };
  }
  try {
    const cvdData = calculateCVD(state.trades);
    let cvdEmaData: number[] | null = null;
    if (cvdData && cvdData.length > 0) {
      const cvdValues = cvdData.map(d => d.cvd);
      cvdEmaData = await fetchCVDEMA(cvdValues);
    }
    const atrValue = calculateATR(state.ohlcv, 14); // Using a common period of 14 for ATR

    return {
      cvd: cvdData,
      cvdEma: cvdEmaData,
      atr: atrValue,
      error: null
    };
  } catch (error: any) {
    console.error('Error in Indicator Calculation Node:', error);
    return { error: error.message || 'Unknown error in indicator calculation' };
  }
}

// Define the SignalGenerationNode
async function signalGenerationNode(state: AppState): Promise<Partial<AppState>> {
  console.log(`\n--- Executing Signal Generation Node for ${state.symbol} ---`);
  try {
    // Pass ATR to generateSignals if it's used there.
    // For now, generateSignals signature doesn't include ATR, but it's available in state.
    const signal = generateSignals(
      state.ohlcv ?? null,
      state.orderBook ?? null,
      state.cvd ?? null,
      // state.cvdEma, // Not currently used by generateSignals
      // state.atr // Not currently used by generateSignals but available if needed
    );
    console.log(`Signal generated: ${signal.type} - ${signal.reason}`);
    return { signal, error: null };
  } catch (error: any) {
    console.error('Error in Signal Generation Node:', error);
    return { error: error.message || 'Unknown error in signal generation' };
  }
}

// Define the DecisionMakingNode
async function decisionMakingNode(state: AppState): Promise<Partial<AppState>> {
  console.log(`\n--- Executing Decision Making Node for ${state.symbol} ---`);
  if (!state.signal) {
    console.warn('No signal available to make a decision.');
    return { decision: null, error: 'Signal data is missing for decision making' };
  }
  if (state.signal.type === 'hold') {
    console.log('Signal is "hold", no decision needed from Grok.');
    return {
      decision: {
        tradeId: `hold_${Date.now()}`,
        timestamp: Date.now(),
        symbol: state.symbol,
        signal: state.signal,
        confirmed: false,
        grokReasoning: "Signal was 'hold'."
      },
      error: null
    };
  }

  // Determine current price for Grok evaluation (e.g., from last OHLCV close)
  let currentPriceForDecision = state.ohlcv && state.ohlcv.length > 0 ? state.ohlcv[state.ohlcv.length - 1][4] : null;
  if (typeof currentPriceForDecision !== 'number') {
    // Fallback if OHLCV close is not available
    currentPriceForDecision = state.orderBook?.bids?.[0]?.[0] ?? state.orderBook?.asks?.[0]?.[0] ?? 0;
  }
  if (typeof currentPriceForDecision !== 'number' || currentPriceForDecision <= 0) {
    console.warn('Could not determine a valid current price for Grok decision.');
    return { decision: null, error: 'Could not determine current price for decision.' };
  }


  try {
    const decision = await evaluateSignalWithGrok(state.symbol, state.signal, currentPriceForDecision, state.atr ?? null);
    console.log(`Grok decision: ${decision.confirmed ? 'CONFIRMED' : 'REJECTED'} - ${decision.grokReasoning}`);
    if (decision.error) {
        console.error(`Grok evaluation resulted in an error: ${decision.error}`);
    }
    return { decision, error: decision.error || null };
  } catch (error: any) {
    console.error('Error in Decision Making Node:', error);
    return { error: error.message || 'Unknown error in decision making' };
  }
}

// Define the OrderExecutionNode
async function orderExecutionNode(state: AppState): Promise<Partial<AppState>> {
  console.log(`\n--- Executing Order Execution Node for ${state.symbol} ---`);
  if (!state.decision) {
    console.warn('No decision available to execute trade.');
    return { execution: null, error: 'Decision data is missing for trade execution' };
  }
  try {
    const executionResult = await executeTrade(state.decision);
    console.log(`Trade execution result: ${executionResult.status} - ${executionResult.message}`);
    return { execution: executionResult, error: executionResult.status === 'failed' ? executionResult.message : null };
  } catch (error: any) {
    console.error('Error in Order Execution Node:', error);
    return { error: error.message || 'Unknown error in order execution' };
  }
}

// Create the graph
const workflow = new StateGraph(appState)
  .addNode("dataCollector", dataCollectionNode)
  .addNode("indicatorCalculator", indicatorCalculationNode)
  .addNode("signalGenerator", signalGenerationNode)
  .addNode("decisionMaker", decisionMakingNode)
  .addNode("orderExecutor", orderExecutionNode) // Added order executor node
  .addEdge(START, "dataCollector")
  .addEdge("dataCollector", "indicatorCalculator")
  .addEdge("indicatorCalculator", "signalGenerator")
  .addEdge("signalGenerator", "decisionMaker")
  .addEdge("decisionMaker", "orderExecutor") // Decision maker now goes to order executor
  .addEdge("orderExecutor", END); // For now, end after order execution

const app = workflow.compile();

console.log("Graph compiled.");

// Example of how to run the graph (for testing purposes)
async function runGraph() {
  const initialState: AppState = {
    symbol: 'BTC/USDT', // Default or passed-in symbol
  };

  console.log("\n--- Running Graph ---");
  try {
    const finalState = await app.invoke(initialState);
    console.log("\n--- Graph Execution Finished ---");
    console.log("Final State:");
    if (finalState.error) {
      console.error("Error in final state:", finalState.error);
    }
    // console.log("OHLCV Data:", finalState.ohlcv ? `${finalState.ohlcv.length} candles` : 'No OHLCV data');
    // console.log("Order Book:", finalState.orderBook ? `Bids: ${finalState.orderBook.bids.length}, Asks: ${finalState.orderBook.asks.length}` : 'No Order Book data');
    // console.log("Trades Data:", finalState.trades ? `${finalState.trades.length} trades` : 'No Trades data');
    // console.log("CVD Data:", finalState.cvd ? `${finalState.cvd.length} points` : 'No CVD data');
    // console.log("CVD EMA Data:", finalState.cvdEma ? `${finalState.cvdEma.length} points` : 'No CVD EMA data');
    // console.log("Signal:", finalState.signal ? `${finalState.signal.type} - ${finalState.signal.reason}` : 'No Signal');
    // console.log("Decision:", finalState.decision ? `${finalState.decision.confirmed} - ${finalState.decision.grokReasoning}` : 'No Decision');
    // console.log("Execution:", finalState.execution ? `${finalState.execution.status} - ${finalState.execution.message}` : 'No Execution');

    // For more detailed inspection if needed:
    // console.log("Sample OHLCV:", finalState.ohlcv ? finalState.ohlcv[0] : 'N/A');
    // console.log("Sample Order Book Bid:", finalState.orderBook && finalState.orderBook.bids.length > 0 ? finalState.orderBook.bids[0] : 'N/A');
    // console.log("Sample Trade:", finalState.trades && finalState.trades.length > 0 ? finalState.trades[0] : 'N/A');
    // console.log("Sample CVD point:", finalState.cvd && finalState.cvd.length > 0 ? finalState.cvd[0] : 'N/A');
    // console.log("Sample CVD EMA point:", finalState.cvdEma && finalState.cvdEma.length > 0 ? finalState.cvdEma[0] : 'N/A');
    // console.log("Full Signal Details:", finalState.signal);
    // console.log("Full Decision Details:", finalState.decision);
    // console.log("Full Execution Details:", finalState.execution);
  } catch (e) {
    console.error("Error running graph:", e);
  }
}

// To run this test:
// 1. Ensure you have a .env file with PHEMEX_API_KEY and PHEMEX_API_SECRET (and PHEMEX_IS_TESTNET=true for testnet)
// 2. You can run this file using tsx: `npx tsx src/graph.ts`
runGraph();

export { app, AppState };


