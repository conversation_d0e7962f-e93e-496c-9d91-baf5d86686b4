"use strict";
/* __LC_ALLOW_ENTRYPOINT_SIDE_EFFECTS__ */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesAnnotation = void 0;
const annotation_js_1 = require("./annotation.cjs");
const message_js_1 = require("./message.cjs");
/**
 * Prebuilt state annotation that combines returned messages.
 * Can handle standard messages and special modifiers like {@link RemoveMessage}
 * instances.
 *
 * Specifically, importing and using the prebuilt MessagesAnnotation like this:
 *
 * @example
 * ```ts
 * import { MessagesAnnotation, StateGraph } from "@langchain/langgraph";
 *
 * const graph = new StateGraph(MessagesAnnotation)
 *   .addNode(...)
 *   ...
 * ```
 *
 * Is equivalent to initializing your state manually like this:
 *
 * @example
 * ```ts
 * import { BaseMessage } from "@langchain/core/messages";
 * import { Annotation, StateGraph, messagesStateReducer } from "@langchain/langgraph";
 *
 * export const StateAnnotation = Annotation.Root({
 *   messages: Annotation<BaseMessage[]>({
 *     reducer: messagesStateReducer,
 *     default: () => [],
 *   }),
 * });
 *
 * const graph = new StateGraph(StateAnnotation)
 *   .addNode(...)
 *   ...
 * ```
 */
exports.MessagesAnnotation = annotation_js_1.Annotation.Root({
    messages: (0, annotation_js_1.Annotation)({
        reducer: message_js_1.messagesStateReducer,
        default: () => [],
    }),
});
//# sourceMappingURL=messages_annotation.js.map