"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCheckpoint = exports.emptyChannels = exports.BaseChannel = exports.isBaseChannel = void 0;
const langgraph_checkpoint_1 = require("@langchain/langgraph-checkpoint");
const errors_js_1 = require("../errors.cjs");
function isBaseChannel(obj) {
    return obj != null && obj.lg_is_channel === true;
}
exports.isBaseChannel = isBaseChannel;
class BaseChannel {
    constructor() {
        Object.defineProperty(this, "ValueType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "UpdateType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /** @ignore */
        Object.defineProperty(this, "lg_is_channel", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
    }
    /**
     * Mark the current value of the channel as consumed. By default, no-op.
     * This is called by <PERSON><PERSON> before the start of the next step, for all
     * channels that triggered a node. If the channel was updated, return true.
     */
    consume() {
        return false;
    }
}
exports.BaseChannel = BaseChannel;
function emptyChannels(channels, checkpoint) {
    const filteredChannels = Object.fromEntries(Object.entries(channels).filter(([, value]) => isBaseChannel(value)));
    const newChannels = {};
    for (const k in filteredChannels) {
        if (Object.prototype.hasOwnProperty.call(filteredChannels, k)) {
            const channelValue = checkpoint.channel_values[k];
            newChannels[k] = filteredChannels[k].fromCheckpoint(channelValue);
        }
    }
    return newChannels;
}
exports.emptyChannels = emptyChannels;
function createCheckpoint(checkpoint, channels, step) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let values;
    if (channels === undefined) {
        values = checkpoint.channel_values;
    }
    else {
        values = {};
        for (const k of Object.keys(channels)) {
            try {
                values[k] = channels[k].checkpoint();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            }
            catch (error) {
                if (error.name === errors_js_1.EmptyChannelError.unminifiable_name) {
                    // no-op
                }
                else {
                    throw error; // Rethrow unexpected errors
                }
            }
        }
    }
    return {
        v: 1,
        id: (0, langgraph_checkpoint_1.uuid6)(step),
        ts: new Date().toISOString(),
        channel_values: values,
        channel_versions: { ...checkpoint.channel_versions },
        versions_seen: (0, langgraph_checkpoint_1.deepCopy)(checkpoint.versions_seen),
        pending_sends: checkpoint.pending_sends ?? [],
    };
}
exports.createCheckpoint = createCheckpoint;
//# sourceMappingURL=base.js.map