{"version": 3, "file": "memory.js", "sourceRoot": "", "sources": ["../src/memory.ts"], "names": [], "mappings": "AACA,OAAO,EACL,mBAAmB,EAInB,cAAc,EACd,eAAe,EACf,cAAc,GACf,MAAM,WAAW,CAAC;AAOnB,OAAO,EAAgB,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEvD,SAAS,YAAY,CACnB,QAAgB,EAChB,mBAA2B,EAC3B,YAAoB;IAEpB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,OAAO,WAAY,SAAQ,mBAAmB;IASlD,YAAY,KAA0B;QACpC,KAAK,CAAC,KAAK,CAAC,CAAC;QATf,4EAA4E;QAC5E;;;;mBAGI,EAAE;WAAC;QAEP;;;;mBAAuE,EAAE;WAAC;IAI1E,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,QAAgB,EAChB,YAAoB,EACpB,kBAA2B;QAE3B,IAAI,YAAY,GAAmB,EAAE,CAAC;QACtC,IAAI,kBAAkB,KAAK,SAAS,EAAE;YACpC,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;YACrE,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACnC,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE;gBAC9B,OAAO,OAAO,KAAK,KAAK,CAAC;YAC3B,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;gBACnC,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/C,CAAC,CAAC,IAAI,EAAE,CACX,CAAC;SACH;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAsB;QACnC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC;QACjD,MAAM,aAAa,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE,CAAC;QAC/D,IAAI,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QAE5C,IAAI,aAAa,EAAE;YACjB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YACxE,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,kBAAkB,CAAC,GAAG,KAAK,CAAC;gBACzD,MAAM,GAAG,GAAG,YAAY,CAAC,SAAS,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;gBAClE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC/C,SAAS,EACT,aAAa,EACb,kBAAkB,CACnB,CAAC;gBACF,MAAM,sBAAsB,GAAe;oBACzC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBACpD,aAAa;iBACd,CAAC;gBACF,MAAM,aAAa,GAA6B,MAAM,OAAO,CAAC,GAAG,CAC/D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CACvC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE;oBACjC,OAAO;wBACL,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC;qBAC3C,CAAC;gBACJ,CAAC,CACF,CACF,CAAC;gBACF,MAAM,eAAe,GAAoB;oBACvC,MAAM;oBACN,UAAU,EAAE,sBAAsB;oBAClC,QAAQ,EAAE,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CACpC,MAAM,EACN,QAAQ,CACT,CAAuB;oBACxB,aAAa;iBACd,CAAC;gBACF,IAAI,kBAAkB,KAAK,SAAS,EAAE;oBACpC,eAAe,CAAC,YAAY,GAAG;wBAC7B,YAAY,EAAE;4BACZ,SAAS;4BACT,aAAa;4BACb,aAAa,EAAE,kBAAkB;yBAClC;qBACF,CAAC;iBACH;gBACD,OAAO,eAAe,CAAC;aACxB;SACF;aAAM;YACL,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YAC7D,IAAI,WAAW,KAAK,SAAS,EAAE;gBAC7B,gDAAgD;gBAChD,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACrD,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CACnB,CAAC,CAAC,CAAC,CAAC;gBACL,MAAM,KAAK,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;gBACzC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,kBAAkB,CAAC,GAAG,KAAK,CAAC;gBACzD,MAAM,GAAG,GAAG,YAAY,CAAC,SAAS,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;gBAClE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC/C,SAAS,EACT,aAAa,EACb,kBAAkB,CACnB,CAAC;gBACF,MAAM,sBAAsB,GAAe;oBACzC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;oBACpD,aAAa;iBACd,CAAC;gBACF,MAAM,aAAa,GAA6B,MAAM,OAAO,CAAC,GAAG,CAC/D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CACvC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE;oBACjC,OAAO;wBACL,MAAM;wBACN,OAAO;wBACP,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC;qBAC3C,CAAC;gBACJ,CAAC,CACF,CACF,CAAC;gBACF,MAAM,eAAe,GAAoB;oBACvC,MAAM,EAAE;wBACN,YAAY,EAAE;4BACZ,SAAS;4BACT,aAAa;4BACb,aAAa;yBACd;qBACF;oBACD,UAAU,EAAE,sBAAsB;oBAClC,QAAQ,EAAE,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CACpC,MAAM,EACN,QAAQ,CACT,CAAuB;oBACxB,aAAa;iBACd,CAAC;gBACF,IAAI,kBAAkB,KAAK,SAAS,EAAE;oBACpC,eAAe,CAAC,YAAY,GAAG;wBAC7B,YAAY,EAAE;4BACZ,SAAS;4BACT,aAAa;4BACb,aAAa,EAAE,kBAAkB;yBAClC;qBACF,CAAC;iBACH;gBACD,OAAO,eAAe,CAAC;aACxB;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,CAAC,IAAI,CACT,MAAsB,EACtB,OAA+B;QAE/B,wCAAwC;QACxC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,SAAS;YAC9C,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC;YAClC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,yBAAyB,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC;QACrE,MAAM,kBAAkB,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC;QAE9D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;YAChC,KAAK,MAAM,mBAAmB,IAAI,MAAM,CAAC,IAAI,CAC3C,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAC7B,EAAE;gBACD,IACE,yBAAyB,KAAK,SAAS;oBACvC,mBAAmB,KAAK,yBAAyB,EACjD;oBACA,SAAS;iBACV;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;gBACxE,MAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAClE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzB,CAAC;gBAEF,KAAK,MAAM,CACT,YAAY,EACZ,CAAC,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,EAC9C,IAAI,iBAAiB,EAAE;oBACtB,sCAAsC;oBACtC,IAAI,kBAAkB,IAAI,YAAY,KAAK,kBAAkB,EAAE;wBAC7D,SAAS;qBACV;oBAED,6CAA6C;oBAC7C,IACE,MAAM;wBACN,MAAM,CAAC,YAAY,EAAE,aAAa;wBAClC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,aAAa,EACjD;wBACA,SAAS;qBACV;oBAED,iBAAiB;oBACjB,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAC3C,MAAM,EACN,WAAW,CACZ,CAAuB,CAAC;oBAEzB,IACE,MAAM;wBACN,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAC3B,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CACd,QAA+C,CAAC,GAAG,CAAC,KAAK,KAAK,CAClE,EACD;wBACA,SAAS;qBACV;oBAED,uBAAuB;oBACvB,IAAI,KAAK,KAAK,SAAS,EAAE;wBACvB,IAAI,KAAK,IAAI,CAAC;4BAAE,MAAM;wBACtB,KAAK,IAAI,CAAC,CAAC;qBACZ;oBAED,MAAM,GAAG,GAAG,YAAY,CAAC,QAAQ,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAC;oBACtE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;oBACrD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC/C,QAAQ,EACR,mBAAmB,EACnB,kBAAkB,CACnB,CAAC;oBAEF,MAAM,aAAa,GAA6B,MAAM,OAAO,CAAC,GAAG,CAC/D,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE;wBAC5C,OAAO;4BACL,MAAM;4BACN,OAAO;4BACP,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC;yBAC3C,CAAC;oBACJ,CAAC,CAAC,CACH,CAAC;oBAEF,MAAM,sBAAsB,GAAG;wBAC7B,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBACpD,aAAa;qBACd,CAAC;oBAEF,MAAM,eAAe,GAAoB;wBACvC,MAAM,EAAE;4BACN,YAAY,EAAE;gCACZ,SAAS,EAAE,QAAQ;gCACnB,aAAa,EAAE,mBAAmB;gCAClC,aAAa,EAAE,YAAY;6BAC5B;yBACF;wBACD,UAAU,EAAE,sBAAsB;wBAClC,QAAQ;wBACR,aAAa;qBACd,CAAC;oBACF,IAAI,kBAAkB,KAAK,SAAS,EAAE;wBACpC,eAAe,CAAC,YAAY,GAAG;4BAC7B,YAAY,EAAE;gCACZ,SAAS,EAAE,QAAQ;gCACnB,aAAa,EAAE,mBAAmB;gCAClC,aAAa,EAAE,kBAAkB;6BAClC;yBACF,CAAC;qBACH;oBACD,MAAM,eAAe,CAAC;iBACvB;aACF;SACF;IACH,CAAC;IAED,KAAK,CAAC,GAAG,CACP,MAAsB,EACtB,UAAsB,EACtB,QAA4B;QAE5B,MAAM,kBAAkB,GAAwB,cAAc,CAAC,UAAU,CAAC,CAAC;QAC3E,OAAO,kBAAkB,CAAC,aAAa,CAAC;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC;QAChD,MAAM,mBAAmB,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE,CAAC;QACrE,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,6HAA6H,CAC9H,CAAC;SACH;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAC7B;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,EAAE;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;SAClD;QAED,MAAM,CAAC,EAAE,oBAAoB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAC3E,MAAM,CAAC,EAAE,kBAAkB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG;YAC3D,oBAAoB;YACpB,kBAAkB;YAClB,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,SAAS;SAC9C,CAAC;QAEF,OAAO;YACL,YAAY,EAAE;gBACZ,SAAS,EAAE,QAAQ;gBACnB,aAAa,EAAE,mBAAmB;gBAClC,aAAa,EAAE,UAAU,CAAC,EAAE;aAC7B;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAsB,EACtB,MAAsB,EACtB,MAAc;QAEd,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC;QAChD,MAAM,mBAAmB,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC;QAC/D,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC;QACxD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,MAAM,IAAI,KAAK,CACb,wHAAwH,CACzH,CAAC;SACH;QACD,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,MAAM,IAAI,KAAK,CACb,6HAA6H,CAC9H,CAAC;SACH;QACD,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,mBAAmB,EAAE,YAAY,CAAC,CAAC;QAC3E,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;SAC5B;QACD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,EAAE;YACvC,MAAM,CAAC,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAqB;gBACjC,MAAM;gBACN,cAAc,CAAC,OAAO,CAAC,IAAI,GAAG;aAC/B,CAAC;YACF,MAAM,WAAW,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YACpD,IAAI,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,IAAI,WAAW,IAAI,YAAY,EAAE;gBACnE,OAAO;aACR;YACD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}