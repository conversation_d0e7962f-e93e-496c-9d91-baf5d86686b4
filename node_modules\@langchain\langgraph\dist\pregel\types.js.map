{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/pregel/types.ts"], "names": [], "mappings": "AA2cA,MAAM,OAAO,IAAI;IAaf,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAe;QAZhE;;;;;WAAyD;QAEzD;;;;;WAAa;QAEb;;;;;WAAe;QAEf;;;;;WAAoB;QAEpB;;;;;WAAoB;QAEpB;;;;mBAAqB,MAAM;WAAC;QAG1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;CACF;AAED,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,WAAW,IAAI,KAAK;QACpB,KAAK,CAAC,SAAS,KAAK,MAAM,CAC3B,CAAC;AACJ,CAAC"}