"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isCall = exports.Call = void 0;
class Call {
    constructor({ func, name, input, retry, callbacks }) {
        Object.defineProperty(this, "func", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "input", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "retry", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "callbacks", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "__lg_type", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "call"
        });
        this.func = func;
        this.name = name;
        this.input = input;
        this.retry = retry;
        this.callbacks = callbacks;
    }
}
exports.Call = Call;
function isCall(value) {
    return (typeof value === "object" &&
        value !== null &&
        "__lg_type" in value &&
        value.__lg_type === "call");
}
exports.isCall = isCall;
//# sourceMappingURL=types.js.map