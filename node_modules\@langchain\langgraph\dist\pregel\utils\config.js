import { AsyncLocalStorageProviderSingleton } from "@langchain/core/singletons";
import { CHECKPOINT_NAMESPACE_END, CHECKPOINT_NAMESPACE_SEPARATOR, CONFIG_KEY_SCRATCHPAD, } from "../../constants.js";
const COPIABLE_KEYS = ["tags", "metadata", "callbacks", "configurable"];
const CONFIG_KEYS = [
    "tags",
    "metadata",
    "callbacks",
    "runName",
    "maxConcurrency",
    "recursionLimit",
    "configurable",
    "runId",
    "outputKeys",
    "streamMode",
    "store",
    "writer",
    "interruptBefore",
    "interruptAfter",
    "signal",
];
const DEFAULT_RECURSION_LIMIT = 25;
export function ensureLangGraphConfig(...configs) {
    const empty = {
        tags: [],
        metadata: {},
        callbacks: undefined,
        recursionLimit: DEFAULT_RECURSION_LIMIT,
        configurable: {},
    };
    const implicitConfig = AsyncLocalStorageProviderSingleton.getRunnableConfig();
    if (implicitConfig !== undefined) {
        for (const [k, v] of Object.entries(implicitConfig)) {
            if (v !== undefined) {
                if (COPIABLE_KEYS.includes(k)) {
                    let copiedValue;
                    if (Array.isArray(v)) {
                        copiedValue = [...v];
                    }
                    else if (typeof v === "object") {
                        if (k === "callbacks" &&
                            "copy" in v &&
                            typeof v.copy === "function") {
                            copiedValue = v.copy();
                        }
                        else {
                            copiedValue = { ...v };
                        }
                    }
                    else {
                        copiedValue = v;
                    }
                    empty[k] = copiedValue;
                }
                else {
                    empty[k] = v;
                }
            }
        }
    }
    for (const config of configs) {
        if (config === undefined) {
            continue;
        }
        for (const [k, v] of Object.entries(config)) {
            if (v !== undefined && CONFIG_KEYS.includes(k)) {
                empty[k] = v;
            }
        }
    }
    for (const [key, value] of Object.entries(empty.configurable)) {
        empty.metadata = empty.metadata ?? {};
        if (!key.startsWith("__") &&
            (typeof value === "string" ||
                typeof value === "number" ||
                typeof value === "boolean") &&
            !(key in empty.metadata)) {
            empty.metadata[key] = value;
        }
    }
    return empty;
}
/**
 * A helper utility function that returns the {@link BaseStore} that was set when the graph was initialized
 *
 * @returns a reference to the {@link BaseStore} that was set when the graph was initialized
 */
export function getStore() {
    const config = AsyncLocalStorageProviderSingleton.getRunnableConfig();
    return config?.store;
}
/**
 * A helper utility function that returns the {@link LangGraphRunnableConfig#writer} if "custom" stream mode is enabled, otherwise undefined
 *
 * @returns a reference to the {@link LangGraphRunnableConfig#writer} if "custom" stream mode is enabled, otherwise undefined
 */
export function getWriter() {
    const config = AsyncLocalStorageProviderSingleton.getRunnableConfig();
    return config?.configurable?.writer;
}
/**
 * A helper utility function that returns the {@link LangGraphRunnableConfig} that was set when the graph was initialized
 *
 * @returns the {@link LangGraphRunnableConfig} that was set when the graph was initialized
 */
export function getConfig() {
    return AsyncLocalStorageProviderSingleton.getRunnableConfig();
}
/**
 * A helper utility function that returns the input for the currently executing task
 *
 * @returns the input for the currently executing task
 */
export function getCurrentTaskInput() {
    const config = AsyncLocalStorageProviderSingleton.getRunnableConfig();
    if (config === undefined) {
        throw new Error("Config not retrievable. This is likely because you are running in an environment without support for AsyncLocalStorage.");
    }
    if (config.configurable?.[CONFIG_KEY_SCRATCHPAD]?.currentTaskInput === undefined) {
        throw new Error("BUG: internal scratchpad not initialized.");
    }
    return config.configurable[CONFIG_KEY_SCRATCHPAD].currentTaskInput;
}
export function recastCheckpointNamespace(namespace) {
    return namespace
        .split(CHECKPOINT_NAMESPACE_SEPARATOR)
        .filter((part) => !part.match(/^\d+$/))
        .map((part) => part.split(CHECKPOINT_NAMESPACE_END)[0])
        .join(CHECKPOINT_NAMESPACE_SEPARATOR);
}
export function getParentCheckpointNamespace(namespace) {
    const parts = namespace.split(CHECKPOINT_NAMESPACE_SEPARATOR);
    while (parts.length > 1 && parts[parts.length - 1].match(/^\d+$/)) {
        parts.pop();
    }
    return parts.slice(0, -1).join(CHECKPOINT_NAMESPACE_SEPARATOR);
}
//# sourceMappingURL=config.js.map