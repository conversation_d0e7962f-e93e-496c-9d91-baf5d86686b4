{"version": 3, "file": "remote.js", "sourceRoot": "", "sources": ["../../src/pregel/remote.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,MAAM,GAGP,MAAM,0BAA0B,CAAC;AAClC,OAAO,EACL,KAAK,IAAI,aAAa,GAEvB,MAAM,iCAAiC,CAAC;AACzC,OAAO,EACL,YAAY,EACZ,QAAQ,GAET,MAAM,2BAA2B,CAAC;AAQnC,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAEzD,OAAO,EAEL,cAAc,EAGd,eAAe,GAChB,MAAM,WAAW,CAAC;AAWnB,OAAO,EACL,8BAA8B,EAC9B,iBAAiB,EACjB,SAAS,EACT,SAAS,GACV,MAAM,iBAAiB,CAAC;AAgBzB,8DAA8D;AAC9D,MAAM,gBAAgB,GAAG,CAAC,GAAQ,EAAO,EAAE;IACzC,IAAI,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3C,OAAO,GAAG,CAAC;KACZ;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACtB,OAAO,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;KAClC;IAED,2EAA2E;IAC3E,IAAI,aAAa,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QAC1B,OAAO;YACL,GAAG,IAAI,CAAC,IAAI;YACZ,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE;SACpB,CAAC;KACH;IAED,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1E,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,cAAc,GAAG,CACrB,UAAsC,EACtC,oBAAgC,SAAS,EACzC,EAAE;IACF,MAAM,kBAAkB,GAAiB,EAAE,CAAC;IAC5C,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,SAAS,GAAG,IAAI,CAAC;IAErB,IACE,UAAU,KAAK,SAAS;QACxB,CAAC,OAAO,UAAU,KAAK,QAAQ;YAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EACvD;QACA,SAAS,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QACrE,kBAAkB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;KACpC;SAAM;QACL,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5C;IACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC1C,UAAU,GAAG,IAAI,CAAC;KACnB;SAAM;QACL,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACpC;IACD,OAAO;QACL,kBAAkB;QAClB,UAAU;QACV,SAAS;KACV,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAM,OAAO,WASX,SAAQ,QAIP;IAGD,MAAM,CAAC,OAAO;QACZ,OAAO,aAAa,CAAC;IACvB,CAAC;IAgBD,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QAfhB;;;;mBAAe,CAAC,WAAW,EAAE,QAAQ,CAAC;WAAC;QAEvC;;;;mBAAe,IAAI;WAAC;QAEpB;;;;;WAAwB;QAExB;;;;;WAAgB;QAEhB;;;;;WAAyB;QAEzB;;;;;WAAkD;QAElD;;;;;WAAiD;QAK/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,MAAM;YACT,MAAM,CAAC,MAAM;gBACb,IAAI,MAAM,CAAC;oBACT,MAAM,EAAE,MAAM,CAAC,GAAG;oBAClB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,cAAc,EAAE,MAAM,CAAC,OAAO;iBAC/B,CAAC,CAAC;QACL,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAC9C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;IAC9C,CAAC;IAED,6DAA6D;IAC7D,2EAA2E;IAClE,UAAU,CAAC,MAAsB;QACxC,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,8DAA8D;QAC9D,OAAO,IAAK,IAAI,CAAC,WAAmB,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC;IAC1E,CAAC;IAES,eAAe,CAAC,MAAsB;QAC9C,MAAM,wBAAwB,GAAG,IAAI,GAAG,CAAC;YACvC,WAAW;YACX,gBAAgB;YAChB,eAAe;YACf,eAAe;SAChB,CAAC,CAAC;QAEH,8DAA8D;QAC9D,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAO,EAAE;YACpC,4DAA4D;YAC5D,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAClC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBACtB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;iBACvC;qBAAM;oBACL,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CACzD,CAAC;iBACH;aACF;YAED,IAAI;gBACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACpB,OAAO,GAAG,CAAC;aACZ;YAAC,MAAM;gBACN,OAAO,IAAI,CAAC;aACb;QACH,CAAC,CAAC;QAEF,sDAAsD;QACtD,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAE5C,2DAA2D;QAC3D,uCAAuC;QACvC,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,CACxC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CACvD,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CACxE,CACF,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,EAAE;YAChC,QAAQ,EAAE,eAAe,CAAC,QAAQ,IAAI,EAAE;YACxC,YAAY,EAAE,eAAe;SAC9B,CAAC;IACJ,CAAC;IAES,UAAU,CAAC,UAAmC;QACtD,OAAO;YACL,YAAY,EAAE;gBACZ,SAAS,EAAE,UAAU,CAAC,SAAS;gBAC/B,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,cAAc,EAAE,UAAU,CAAC,cAAc,IAAI,EAAE;aAChD;SACF,CAAC;IACJ,CAAC;IAES,cAAc,CAAC,MAAuB;QAC9C,IAAI,MAAM,EAAE,YAAY,KAAK,SAAS,EAAE;YACtC,OAAO,SAAS,CAAC;SAClB;QAED,MAAM,cAAc,GAAG;YACrB,WAAW;YACX,eAAe;YACf,eAAe;YACf,gBAAgB;SACR,CAAC;QAEX,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CACnC,cAAc;aACX,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,YAAa,CAAC,GAAG,CAAC,CAAC,CAAC;aAC9C,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAC,CAC/C,CAAC;QAEF,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;IACrE,CAAC;IAES,oBAAoB,CAAC,KAAkB;QAC/C,MAAM,KAAK,GAA4B,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC9D,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS;gBACvD,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,6CAA6C;gBAC7C,KAAK,EAAE,IAAI,CAAC,KAAK;oBACf,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;oBACvC,CAAC,CAAC,IAAI,CAAC,UAAU;wBACjB,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE;wBACnC,CAAC,CAAC,SAAS;gBACb,8DAA8D;gBAC9D,MAAM,EAAG,IAAY,CAAC,MAAM;aAC7B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,MAAM,EAAE;gBACN,YAAY,EAAE;oBACZ,SAAS,EAAE,KAAK,CAAC,UAAU,CAAC,SAAS;oBACrC,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,aAAa;oBAC7C,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,aAAa;oBAC7C,cAAc,EAAE,KAAK,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE;iBACtD;aACF;YACD,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACtB,CAAC,CAAE,KAAK,CAAC,QAA+B;gBACxC,CAAC,CAAC,SAAS;YACb,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,SAAS;YACxC,YAAY,EAAE,KAAK,CAAC,iBAAiB;gBACnC,CAAC,CAAC;oBACE,YAAY,EAAE;wBACZ,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,SAAS;wBAC5C,aAAa,EAAE,KAAK,CAAC,iBAAiB,CAAC,aAAa;wBACpD,aAAa,EAAE,KAAK,CAAC,iBAAiB,CAAC,aAAa;wBACpD,cAAc,EAAE,KAAK,CAAC,iBAAiB,CAAC,cAAc,IAAI,EAAE;qBAC7D;iBACF;gBACH,CAAC,CAAC,SAAS;YACb,KAAK;SACN,CAAC;IACJ,CAAC;IAEQ,KAAK,CAAC,MAAM,CACnB,KAAsB,EACtB,OAA+D;QAE/D,IAAI,SAAS,CAAC;QACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACtC,GAAG,OAAO;YACV,UAAU,EAAE,QAAQ;SACrB,CAAC,CAAC;QACH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE;YAChC,SAAS,GAAG,KAAK,CAAC;SACnB;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAiBQ,YAAY,CACnB,MAAuB,EACvB,QAGC;QAED,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEQ,KAAK,CAAC,CAAC,eAAe,CAC7B,KAAsB,EACtB,OAA+D;QAE/D,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAE3D,MAAM,sBAAsB,GAAG,OAAO,EAAE,YAAY,EAAE,CAAC,iBAAiB,CAAC,CAAC;QAE1E,MAAM,eAAe,GACnB,OAAO,EAAE,SAAS,IAAI,sBAAsB,KAAK,SAAS,CAAC;QAE7D,MAAM,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC;QACzE,MAAM,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;QAEtE,MAAM,EAAE,kBAAkB,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,cAAc,CAClE,OAAO,EAAE,UAAU,CACpB,CAAC;QAEF,MAAM,mBAAmB,GAAG;YAC1B,GAAG,IAAI,GAAG,CAAC;gBACT,GAAG,kBAAkB;gBACrB,GAAG,CAAC,sBAAsB,EAAE,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;aAChD,CAAC;SACH,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,IAAI,IAAI,KAAK,UAAU;gBAAE,OAAO,gBAAgB,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC;QACZ,IAAI,eAAe,CAAC;QACpB,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE;YACpB,kDAAkD;YAClD,OAAO,GAAG,KAAK,CAAC,MAAM,EAA6B,CAAC;YACpD,eAAe,GAAG,SAAS,CAAC;SAC7B;aAAM;YACL,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAC/C,eAAe,CAAC,YAAY,CAAC,SAAmB,EAChD,IAAI,CAAC,OAAO,EACZ;YACE,OAAO;YACP,KAAK,EAAE,eAAe;YACtB,MAAM,EAAE,eAAe;YACvB,UAAU,EAAE,mBAAmB;YAC/B,eAAe,EAAE,eAA2B;YAC5C,cAAc,EAAE,cAA0B;YAC1C,eAAe;YACf,WAAW,EAAE,QAAQ;SACtB,CACF,EAAE;YACD,IAAI,IAAI,CAAC;YACT,IAAI,SAAmB,CAAC;YACxB,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;gBACxD,MAAM,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CACvC,8BAA8B,CAC/B,CAAC;gBACF,gDAAgD;gBAChD,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBAC1B,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtC;iBAAM;gBACL,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;gBACnB,SAAS,GAAG,EAAE,CAAC;aAChB;YACD,MAAM,eAAe,GAAG,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC;YAC7D,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;gBACvC,SAAS,GAAG,eAAe;qBACxB,KAAK,CAAC,8BAA8B,CAAC;qBACrC,MAAM,CAAC,SAAS,CAAC,CAAC;aACtB;YACD,IACE,sBAAsB,KAAK,SAAS;gBACpC,sBAAsB,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAC9C;gBACA,sBAAsB,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5D;YACD,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBACrC,IACE,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;oBAC9B,KAAK,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,SAAS,EACrC;oBACA,MAAM,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;iBACjD;gBACD,IAAI,CAAC,UAAU,EAAE;oBACf,SAAS;iBACV;aACF;iBAAM,IAAI,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC3C,MAAM,IAAI,eAAe,CACvB,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;oBAC5B,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAC/B,CAAC;aACH;YACD,IACE,CAAC,kBAAkB,CAAC,QAAQ,CAC1B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAe,CACnE,EACD;gBACA,SAAS;aACV;YACD,IAAI,OAAO,EAAE,SAAS,EAAE;gBACtB,IAAI,SAAS,EAAE;oBACb,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;iBAC/B;qBAAM;oBACL,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;iBACrC;aACF;iBAAM,IAAI,SAAS,EAAE;gBACpB,MAAM,KAAK,CAAC,IAAI,CAAC;aAClB;iBAAM;gBACL,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;aAC1B;SACF;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CACf,WAAoC,EACpC,MAA+B,EAC/B,MAAe;QAEf,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC5D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CACpD,YAAY,CAAC,YAAY,EAAE,SAAS,EACpC,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAClE,CAAC;QACF,uBAAuB;QACvB,8DAA8D;QAC9D,OAAO,IAAI,CAAC,UAAU,CAAE,QAAgB,CAAC,UAAU,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,CAAC,eAAe,CACpB,MAAsB,EACtB,OAA+B;QAE/B,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CACjD,YAAY,CAAC,YAAY,EAAE,SAAS,EACpC;YACE,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE;YAC3B,iBAAiB;YACjB,8DAA8D;YAC9D,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAQ;YACnD,QAAQ,EAAE,OAAO,EAAE,MAAM;YACzB,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;SAC9C,CACF,CAAC;QACF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;SACxC;IACH,CAAC;IAES,iBAAiB,CACzB,KAME;QAEF,MAAM,QAAQ,GAAiC,EAAE,CAAC;QAClD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;YACvB,QAAQ,CAAC,MAAM,CAAC,GAAG;gBACjB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;gBACrB,IAAI,EAAE,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE;gBACvE,8DAA8D;gBAC9D,IAAI,EAAG,IAAI,CAAC,IAAY,IAAI,EAAE;gBAC9B,QAAQ,EACN,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;aACjE,CAAC;SACH;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAsB,EACtB,OAAiC;QAEjC,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAC9C,YAAY,CAAC,YAAY,EAAE,SAAS,EACpC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EACjC,OAAO,CACR,CAAC;QACF,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,iHAAiH;IACxG,QAAQ,CACf,CAAgD;QAEhD,MAAM,IAAI,KAAK,CACb,2FAA2F,CAC5F,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,MAAqD;QACvE,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;YAChE,IAAI,EAAE,MAAM,EAAE,IAAI;SACnB,CAAC,CAAC;QACH,OAAO,IAAI,aAAa,CAAC;YACvB,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC;YAC1C,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;IAED,qHAAqH;IACrH,YAAY;QAGV,MAAM,IAAI,KAAK,CACb,0GAA0G,CAC3G,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,CAAC,iBAAiB,CACtB,SAAkB,EAClB,OAAO,GAAG,KAAK;QAEf,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE;YACxE,SAAS;YACT,OAAO;SACR,CAAC,CAAC;QAEH,KAAK,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACzD,8DAA8D;YAC9D,MAAM,cAAc,GAAG,IAAK,IAAI,CAAC,WAAmB,CAAC;gBACnD,GAAG,IAAI;gBACP,OAAO,EAAE,WAAW,CAAC,QAAQ;aAC9B,CAAC,CAAC;YACH,MAAM,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;SAC5B;IACH,CAAC;CACF"}