"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getChannelsFromZod = exports.extendMeta = exports.getMeta = exports.withLangGraph = exports.isAnyZodObject = exports.isZodDefault = void 0;
const binop_js_1 = require("../../channels/binop.cjs");
const last_value_js_1 = require("../../channels/last_value.cjs");
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const META_MAP = new WeakMap();
function isZodType(value) {
    return (typeof value === "object" &&
        value != null &&
        "_parse" in value &&
        typeof value._parse === "function");
}
/**
 * @internal
 */
function isZodDefault(value) {
    return (isZodType(value) &&
        "removeDefault" in value &&
        typeof value.removeDefault === "function");
}
exports.isZodDefault = isZodDefault;
/**
 * @internal
 */
function isAnyZodObject(value) {
    return (isZodType(value) &&
        "partial" in value &&
        typeof value.partial === "function");
}
exports.isAnyZodObject = isAnyZodObject;
function withLangGraph(schema, meta) {
    if (meta.reducer && !meta.default) {
        const defaultValue = isZodDefault(schema)
            ? schema._def.defaultValue
            : undefined;
        if (defaultValue != null) {
            // eslint-disable-next-line no-param-reassign
            meta.default = defaultValue;
        }
    }
    META_MAP.set(schema, meta);
    return schema;
}
exports.withLangGraph = withLangGraph;
function getMeta(schema) {
    return META_MAP.get(schema);
}
exports.getMeta = getMeta;
function extendMeta(schema, update) {
    const existingMeta = getMeta(schema);
    const newMeta = update(existingMeta);
    META_MAP.set(schema, newMeta);
}
exports.extendMeta = extendMeta;
function getChannelsFromZod(schema) {
    const channels = {};
    for (const key in schema.shape) {
        if (Object.prototype.hasOwnProperty.call(schema.shape, key)) {
            const keySchema = schema.shape[key];
            const meta = getMeta(keySchema);
            if (meta?.reducer) {
                channels[key] = new binop_js_1.BinaryOperatorAggregate(meta.reducer.fn, meta.default);
            }
            else {
                channels[key] = new last_value_js_1.LastValue();
            }
        }
    }
    return channels;
}
exports.getChannelsFromZod = getChannelsFromZod;
//# sourceMappingURL=state.js.map