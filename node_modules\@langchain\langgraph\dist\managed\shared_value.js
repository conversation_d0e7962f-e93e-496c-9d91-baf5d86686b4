/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChannelKeyPlaceholder, WritableManagedValue, } from "./base.js";
import { InvalidUpdateError } from "../errors.js";
export class SharedValue extends WritableManagedValue {
    constructor(config, params) {
        super(config, params);
        Object.defineProperty(this, "scope", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "store", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "ns", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: {}
        });
        this.scope = params.scope;
        this.store = config.store || null;
        if (!this.store) {
            this.ns = null;
        }
        else if (config.configurable?.[this.scope]) {
            const scopeValue = config.configurable[this.scope];
            const scopedValueString = typeof scopeValue === "string"
                ? scopeValue
                : JSON.stringify(scopeValue);
            this.ns = ["scoped", this.scope, params.key, scopedValueString];
        }
        else {
            throw new Error(`Required scope "${this.scope}" for shared state key was not passed in "config.configurable".`);
        }
    }
    static async initialize(config, args) {
        const instance = new this(config, args);
        await instance.loadStore();
        return instance;
    }
    static on(scope) {
        return {
            cls: SharedValue,
            params: {
                scope,
                key: ChannelKeyPlaceholder,
            },
        };
    }
    call(_step) {
        return { ...this.value };
    }
    processUpdate(values) {
        const writes = [];
        for (const vv of values) {
            for (const [k, v] of Object.entries(vv)) {
                if (v === null) {
                    if (k in this.value) {
                        delete this.value[k];
                        if (this.ns) {
                            writes.push({ namespace: this.ns, key: k, value: null });
                        }
                    }
                }
                else if (typeof v !== "object" || v === null) {
                    throw new InvalidUpdateError("Received a non-object value");
                }
                else {
                    this.value[k] = v;
                    if (this.ns) {
                        writes.push({ namespace: this.ns, key: k, value: v });
                    }
                }
            }
        }
        return writes;
    }
    async update(values) {
        if (!this.store) {
            this.processUpdate(values);
        }
        else {
            await this.store.batch(this.processUpdate(values));
        }
    }
    async loadStore() {
        if (this.store && this.ns) {
            const saved = await this.store.search(this.ns);
            this.value = saved.reduce((acc, item) => {
                acc[item.key] = item.value;
                return acc;
            }, {});
        }
        return false;
    }
}
//# sourceMappingURL=shared_value.js.map