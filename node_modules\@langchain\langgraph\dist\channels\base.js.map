{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/channels/base.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,QAAQ,EACR,KAAK,GAEN,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AAEjD,MAAM,UAAU,aAAa,CAAC,GAAY;IACxC,OAAO,GAAG,IAAI,IAAI,IAAK,GAAmB,CAAC,aAAa,KAAK,IAAI,CAAC;AACpE,CAAC;AAED,MAAM,OAAgB,WAAW;IAAjC;QAKE;;;;;WAAqB;QAErB;;;;;WAAuB;QAOvB,cAAc;QACd;;;;mBAAgB,IAAI;WAAC;IAmDvB,CAAC;IARC;;;;OAIG;IACH,OAAO;QACL,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,MAAM,UAAU,aAAa,CAC3B,QAAY,EACZ,UAA8B;IAE9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,CACzC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAC/D,CAAC;IAER,MAAM,WAAW,GAAG,EAAQ,CAAC;IAC7B,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE;QAChC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE;YAC7D,MAAM,YAAY,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YAClD,WAAW,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;SACnE;KACF;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,UAA8B,EAC9B,QAA4D,EAC5D,IAAY;IAEZ,8DAA8D;IAC9D,IAAI,MAA2B,CAAC;IAChC,IAAI,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC;KACpC;SAAM;QACL,MAAM,GAAG,EAAE,CAAC;QACZ,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrC,IAAI;gBACF,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBACrC,8DAA8D;aAC/D;YAAC,OAAO,KAAU,EAAE;gBACnB,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,CAAC,iBAAiB,EAAE;oBACtD,QAAQ;iBACT;qBAAM;oBACL,MAAM,KAAK,CAAC,CAAC,4BAA4B;iBAC1C;aACF;SACF;KACF;IACD,OAAO;QACL,CAAC,EAAE,CAAC;QACJ,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC;QACf,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QAC5B,cAAc,EAAE,MAAM;QACtB,gBAAgB,EAAE,EAAE,GAAG,UAAU,CAAC,gBAAgB,EAAE;QACpD,aAAa,EAAE,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC;QACjD,aAAa,EAAE,UAAU,CAAC,aAAa,IAAI,EAAE;KAC9C,CAAC;AACJ,CAAC"}