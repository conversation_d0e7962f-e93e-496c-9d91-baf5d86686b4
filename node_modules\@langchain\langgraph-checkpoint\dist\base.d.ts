import type { RunnableConfig } from "@langchain/core/runnables";
import { SerializerProtocol } from "./serde/base.js";
import type { PendingWrite, CheckpointPendingWrite, CheckpointMetadata } from "./types.js";
import { type ChannelProtocol, type SendProtocol } from "./serde/types.js";
type ChannelVersion = number | string;
export type ChannelVersions = Record<string, ChannelVersion>;
export interface Checkpoint<N extends string = string, C extends string = string> {
    /**
     * The version of the checkpoint format. Currently 1
     */
    v: number;
    /**
     * Checkpoint ID {uuid6}
     */
    id: string;
    /**
     * Timestamp {new Date().toISOString()}
     */
    ts: string;
    /**
     * @default {}
     */
    channel_values: Record<C, unknown>;
    /**
     * @default {}
     */
    channel_versions: Record<C, ChannelVersion>;
    /**
     * @default {}
     */
    versions_seen: Record<N, Record<C, ChannelVersion>>;
    /**
     * List of packets sent to nodes but not yet processed.
     * Cleared by the next checkpoint.
     */
    pending_sends: SendProtocol[];
}
export interface ReadonlyCheckpoint extends Readonly<Checkpoint> {
    readonly channel_values: Readonly<Record<string, unknown>>;
    readonly channel_versions: Readonly<Record<string, ChannelVersion>>;
    readonly versions_seen: Readonly<Record<string, Readonly<Record<string, ChannelVersion>>>>;
}
export declare function deepCopy<T>(obj: T): T;
/** @hidden */
export declare function emptyCheckpoint(): Checkpoint;
/** @hidden */
export declare function copyCheckpoint(checkpoint: ReadonlyCheckpoint): Checkpoint;
export interface CheckpointTuple {
    config: RunnableConfig;
    checkpoint: Checkpoint;
    metadata?: CheckpointMetadata;
    parentConfig?: RunnableConfig;
    pendingWrites?: CheckpointPendingWrite[];
}
export type CheckpointListOptions = {
    limit?: number;
    before?: RunnableConfig;
    filter?: Record<string, any>;
};
export declare abstract class BaseCheckpointSaver<V extends string | number = number> {
    serde: SerializerProtocol;
    constructor(serde?: SerializerProtocol);
    get(config: RunnableConfig): Promise<Checkpoint | undefined>;
    abstract getTuple(config: RunnableConfig): Promise<CheckpointTuple | undefined>;
    abstract list(config: RunnableConfig, options?: CheckpointListOptions): AsyncGenerator<CheckpointTuple>;
    abstract put(config: RunnableConfig, checkpoint: Checkpoint, metadata: CheckpointMetadata, newVersions: ChannelVersions): Promise<RunnableConfig>;
    /**
     * Store intermediate writes linked to a checkpoint.
     */
    abstract putWrites(config: RunnableConfig, writes: PendingWrite[], taskId: string): Promise<void>;
    /**
     * Generate the next version ID for a channel.
     *
     * Default is to use integer versions, incrementing by 1. If you override, you can use str/int/float versions,
     * as long as they are monotonically increasing.
     */
    getNextVersion(current: V | undefined, _channel: ChannelProtocol): V;
}
export declare function compareChannelVersions(a: ChannelVersion, b: ChannelVersion): number;
export declare function maxChannelVersion(...versions: ChannelVersion[]): ChannelVersion;
/**
 * Mapping from error type to error index.
 * Regular writes just map to their index in the list of writes being saved.
 * Special writes (e.g. errors) map to negative indices, to avoid those writes from
 * conflicting with regular writes.
 * Each Checkpointer implementation should use this mapping in put_writes.
 */
export declare const WRITES_IDX_MAP: Record<string, number>;
export declare function getCheckpointId(config: RunnableConfig): string;
export {};
