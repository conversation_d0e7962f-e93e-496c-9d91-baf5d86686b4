import type { CheckpointPendingWrite, PendingWrite } from "@langchain/langgraph-checkpoint";
import type { BaseChannel } from "../channels/base.js";
import type { PregelExecutableTask } from "./types.js";
import { Command } from "../constants.js";
export declare function readChannel<C extends PropertyKey>(channels: Record<C, BaseChannel>, chan: C, catchErrors?: boolean, returnException?: boolean): unknown | null;
export declare function readChannels<C extends PropertyKey>(channels: Record<C, BaseChannel>, select: C | Array<C>, skipEmpty?: boolean): Record<string, any> | any;
/**
 * Map input chunk to a sequence of pending writes in the form (channel, value).
 */
export declare function mapCommand(cmd: Command, pendingWrites: CheckpointPendingWrite[]): Generator<[string, string, unknown]>;
/**
 * Map input chunk to a sequence of pending writes in the form [channel, value].
 */
export declare function mapInput<C extends PropertyKey>(inputChannels: C | Array<C>, chunk?: any): Generator<[C, any]>;
/**
 * Map pending writes (a sequence of tuples (channel, value)) to output chunk.
 */
export declare function mapOutputValues<C extends PropertyKey>(outputChannels: C | Array<C>, pendingWrites: readonly PendingWrite<C>[] | true, channels: Record<C, BaseChannel>): Generator<Record<string, any>, any>;
/**
 * Map pending writes (a sequence of tuples (channel, value)) to output chunk.
 * @internal
 *
 * @param outputChannels - The channels to output.
 * @param tasks - The tasks to output.
 * @param cached - Whether the output is cached.
 *
 * @returns A generator that yields the output chunk (if any).
 */
export declare function mapOutputUpdates<N extends PropertyKey, C extends PropertyKey>(outputChannels: C | Array<C>, tasks: readonly [PregelExecutableTask<N, C>, PendingWrite<C>[]][], cached?: boolean): Generator<Record<N, Record<string, unknown> | unknown>>;
export declare function single<T>(iter: IterableIterator<T>): T | null;
