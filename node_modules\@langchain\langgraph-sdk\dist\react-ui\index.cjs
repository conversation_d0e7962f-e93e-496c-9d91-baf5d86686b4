"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isRemoveUIMessage = exports.isUIMessage = exports.uiMessageReducer = exports.experimental_loadShare = exports.LoadExternalComponent = exports.useStreamContext = void 0;
const client_js_1 = require("./client.cjs");
(0, client_js_1.bootstrapUiContext)();
var client_js_2 = require("./client.cjs");
Object.defineProperty(exports, "useStreamContext", { enumerable: true, get: function () { return client_js_2.useStreamContext; } });
Object.defineProperty(exports, "LoadExternalComponent", { enumerable: true, get: function () { return client_js_2.LoadExternalComponent; } });
Object.defineProperty(exports, "experimental_loadShare", { enumerable: true, get: function () { return client_js_2.experimental_loadShare; } });
var types_js_1 = require("./types.cjs");
Object.defineProperty(exports, "uiMessageReducer", { enumerable: true, get: function () { return types_js_1.uiMessageReducer; } });
Object.defineProperty(exports, "isUIMessage", { enumerable: true, get: function () { return types_js_1.isUIMessage; } });
Object.defineProperty(exports, "isRemoveUIMessage", { enumerable: true, get: function () { return types_js_1.isRemoveUIMessage; } });
