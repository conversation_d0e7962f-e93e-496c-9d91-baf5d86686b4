{"version": 3, "file": "io.js", "sourceRoot": "", "sources": ["../../src/pregel/io.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAIhC,OAAO,EACL,OAAO,EACP,OAAO,EACP,KAAK,EACL,SAAS,EACT,YAAY,EACZ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,UAAU,EACV,KAAK,GACN,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAErE,MAAM,UAAU,WAAW,CACzB,QAAgC,EAChC,IAAO,EACP,cAAuB,IAAI,EAC3B,kBAA2B,KAAK;IAEhC,IAAI;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAC5B,8DAA8D;KAC/D;IAAC,OAAO,CAAM,EAAE;QACf,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,iBAAiB,EAAE;YAClD,IAAI,eAAe,EAAE;gBACnB,OAAO,CAAC,CAAC;aACV;iBAAM,IAAI,WAAW,EAAE;gBACtB,OAAO,IAAI,CAAC;aACb;SACF;QACD,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,QAAgC,EAChC,MAAoB,EACpB,YAAqB,IAAI;AACzB,8DAA8D;;IAE9D,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACzB,8DAA8D;QAC9D,MAAM,MAAM,GAAG,EAAoB,CAAC;QACpC,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE;YACtB,IAAI;gBACF,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBACjD,8DAA8D;aAC/D;YAAC,OAAO,CAAM,EAAE;gBACf,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,iBAAiB,EAAE;oBAClD,SAAS;iBACV;aACF;SACF;QACD,OAAO,MAAM,CAAC;KACf;SAAM;QACL,OAAO,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KACtC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,SAAS,CAAC,CAAC,UAAU,CACzB,GAAY,EACZ,aAAuC;IAEvC,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE;QAChC,MAAM,IAAI,kBAAkB,CAAC,2BAA2B,CAAC,CAAC;KAC3D;IACD,IAAI,GAAG,CAAC,IAAI,EAAE;QACZ,IAAI,KAAK,CAAC;QACV,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC3B,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;SAClB;aAAM;YACL,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;SACpB;QACD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;gBACjB,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACnC;iBAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACnC,MAAM,CAAC,YAAY,EAAE,oBAAoB,IAAI,IAAI,IAAI,EAAE,EAAE,WAAW,CAAC,CAAC;aACvE;iBAAM;gBACL,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,IAAI,EAAE,CAC/D,CAAC;aACH;SACF;KACF;IACD,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,IACE,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ;YAC9B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM;YAC9B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EACvC;YACA,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACtD,MAAM,QAAQ,GACZ,aAAa;qBACV,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC;qBAC9C,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBACvB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtB,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;aAC/B;SACF;aAAM;YACL,MAAM,CAAC,YAAY,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;SAC1C;KACF;IACD,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACjD,MAAM,IAAI,KAAK,CACb,yEAAyE,CAC1E,CAAC;SACH;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC7B,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;gBAC/B,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B;SACF;aAAM;YACL,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBAC/C,MAAM,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B;SACF;KACF;AACH,CAAC;AAED;;GAEG;AACH,MAAM,SAAS,CAAC,CAAC,QAAQ,CACvB,aAA2B;AAC3B,8DAA8D;AAC9D,KAAW;AACX,8DAA8D;;IAE9D,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;QACzC,IACE,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC;YAC5B,OAAO,KAAK,KAAK,QAAQ;YACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EACrB;YACA,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE;gBACrB,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAM,CAAC,EAAE;oBAClC,MAAM,CAAC,CAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC1B;aACF;SACF;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CACb,gEAAgE,CACjE,CAAC;SACH;aAAM;YACL,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;SAC9B;KACF;AACH,CAAC;AAED;;GAEG;AACH,MAAM,SAAS,CAAC,CAAC,eAAe,CAC9B,cAA4B,EAC5B,aAAgD,EAChD,QAAgC;AAChC,8DAA8D;;IAE9D,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACjC,IACE,aAAa,KAAK,IAAI;YACtB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAChE;YACA,MAAM,YAAY,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;SAC9C;KACF;SAAM;QACL,IACE,aAAa,KAAK,IAAI;YACtB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,KAAK,cAAc,CAAC,EAC1D;YACA,8DAA8D;YAC9D,MAAM,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAQ,CAAC;SACpD;KACF;AACH,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,SAAS,CAAC,CAAC,gBAAgB,CAC/B,cAA4B,EAC5B,KAAiE,EACjE,MAAgB;IAEhB,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE;QAC9C,OAAO,CACL,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACtE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK;YAClB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CACvB,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACvB,OAAO;KACR;IAED,IAAI,OAAuC,CAAC;IAE5C,IACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,CACjD,EACD;QACA,gGAAgG;QAChG,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CACvC,IAAI,CAAC,MAAM;aACR,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC;aACtC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAiC,CAAC,CAC3E,CAAC;KACH;SAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;QACzC,oEAAoE;QACpE,6CAA6C;QAC7C,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CACvC,IAAI,CAAC,MAAM;aACR,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,KAAK,cAAc,CAAC;aAC9C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAiC,CAAC,CAC3E,CAAC;KACH;SAAM;QACL,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE;YACvC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;YACxB,MAAM,MAAM,GAAsB,EAAuB,CAAC;YAC1D,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE;gBAC3B,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBACjC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;iBACxC;aACF;YAED,IAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAc,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;gBAClE,+DAA+D;gBAC/D,OAAO,MAAM;qBACV,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;qBACjD,GAAG,CACF,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAChB,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAiC,CACjE,CAAC;aACL;iBAAM;gBACL,+DAA+D;gBAC/D,OAAO;oBACL;wBACE,IAAI,CAAC,IAAI;wBACT,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CACzD;qBAC8B;iBAClC,CAAC;aACH;QACH,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,OAAO,GAAG,EAA0B,CAAC;IAE3C,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE;QACnC,IAAI,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,EAAE;YACtB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SACpB;QACD,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;IAED,MAAM,SAAS,GAAG,EAAwB,CAAC;IAC3C,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;QAC1B,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAC9B,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;SACzB;aAAM;YACL,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;SACjC;KACF;IAED,IAAI,MAAM,EAAE;QACV,SAAS,CAAC,cAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;KAC7C;IACD,MAAM,SAAS,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,MAAM,CAAI,IAAyB;IACjD,+CAA+C;IAC/C,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE;QACxB,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}