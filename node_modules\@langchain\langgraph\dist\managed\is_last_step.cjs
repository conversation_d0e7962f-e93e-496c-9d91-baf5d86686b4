"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsLastStepManager = void 0;
const constants_js_1 = require("../constants.cjs");
const base_js_1 = require("./base.cjs");
class IsLastStepManager extends base_js_1.ManagedValue {
    call(step) {
        return step === (this.config.recursionLimit ?? constants_js_1.RECURSION_LIMIT_DEFAULT) - 1;
    }
}
exports.IsLastStepManager = IsLastStepManager;
//# sourceMappingURL=is_last_step.js.map