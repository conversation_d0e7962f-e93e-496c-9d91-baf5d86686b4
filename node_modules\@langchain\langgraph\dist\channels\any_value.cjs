"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnyValue = void 0;
const errors_js_1 = require("../errors.cjs");
const base_js_1 = require("./base.cjs");
/**
 * Stores the last value received, assumes that if multiple values are received, they are all equal.
 *
 * Note: Unlike 'LastValue' if multiple nodes write to this channel in a single step, the values
 * will be continuously overwritten.
 *
 * @internal
 */
class AnyValue extends base_js_1.BaseChannel {
    constructor() {
        super();
        Object.defineProperty(this, "lc_graph_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "AnyValue"
        });
        // value is an array so we don't misinterpret an update to undefined as no write
        Object.defineProperty(this, "value", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
    }
    fromCheckpoint(checkpoint) {
        const empty = new AnyValue();
        if (checkpoint) {
            empty.value = [checkpoint];
        }
        return empty;
    }
    update(values) {
        if (values.length === 0) {
            const updated = this.value.length > 0;
            this.value = [];
            return updated;
        }
        // eslint-disable-next-line prefer-destructuring
        this.value = [values[values.length - 1]];
        return false;
    }
    get() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
    checkpoint() {
        if (this.value.length === 0) {
            throw new errors_js_1.EmptyChannelError();
        }
        return this.value[0];
    }
}
exports.AnyValue = AnyValue;
//# sourceMappingURL=any_value.js.map