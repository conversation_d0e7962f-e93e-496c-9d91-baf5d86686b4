{"name": "@langchain/langgraph-checkpoint", "version": "0.0.17", "description": "Library with base interfaces for LangGraph checkpoint savers.", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langgraphjs.git"}, "scripts": {"build": "yarn turbo:command build:internal --filter=@langchain/langgraph-checkpoint", "build:internal": "yarn clean && yarn lc_build --create-entrypoints --pre --tree-shaking", "clean": "rm -rf dist/ dist-cjs/ .turbo/", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "prepack": "yarn build", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"uuid": "^10.0.0"}, "peerDependencies": {"@langchain/core": ">=0.2.31 <0.4.0"}, "devDependencies": {"@jest/globals": "^29.5.0", "@langchain/scripts": ">=0.1.3 <0.2.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tsconfig/recommended": "^1.0.3", "@types/uuid": "^10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "dotenv": "^16.3.1", "dpdm": "^3.12.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.8.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "prettier": "^2.8.3", "release-it": "^17.6.0", "rollup": "^4.34.9", "ts-jest": "^29.1.0", "tsx": "^4.7.0", "typescript": "^4.9.5 || ^5.4.5"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts"]}