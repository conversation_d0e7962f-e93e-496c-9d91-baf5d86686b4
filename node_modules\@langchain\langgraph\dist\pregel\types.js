export class Call {
    constructor({ func, name, input, retry, callbacks }) {
        Object.defineProperty(this, "func", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "input", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "retry", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "callbacks", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "__lg_type", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "call"
        });
        this.func = func;
        this.name = name;
        this.input = input;
        this.retry = retry;
        this.callbacks = callbacks;
    }
}
export function isCall(value) {
    return (typeof value === "object" &&
        value !== null &&
        "__lg_type" in value &&
        value.__lg_type === "call");
}
//# sourceMappingURL=types.js.map