import { BaseChannel } from "./base.js";
/**
 * Stores the last value received, assumes that if multiple values are received, they are all equal.
 *
 * Note: Unlike 'LastValue' if multiple nodes write to this channel in a single step, the values
 * will be continuously overwritten.
 *
 * @internal
 */
export declare class AnyValue<Value> extends BaseChannel<Value, Value, Value> {
    lc_graph_name: string;
    value: [Value] | [];
    constructor();
    fromCheckpoint(checkpoint?: Value): this;
    update(values: Value[]): boolean;
    get(): Value;
    checkpoint(): Value;
}
