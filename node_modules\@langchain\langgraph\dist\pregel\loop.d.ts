import type { RunnableConfig } from "@langchain/core/runnables";
import type { CallbackManagerForChainRun } from "@langchain/core/callbacks/manager";
import { BaseCheckpointSaver, Checkpoint, PendingWrite, CheckpointPendingWrite, CheckpointMetadata, All, BaseStore, AsyncBatchedStore } from "@langchain/langgraph-checkpoint";
import { BaseChannel } from "../channels/base.js";
import { Call, PregelExecutableTask, StreamMode } from "./types.js";
import { Command } from "../constants.js";
import { PregelNode } from "./read.js";
import { ManagedValueMapping } from "../managed/base.js";
import { LangGraphRunnableConfig } from "./runnable_types.js";
import { IterableReadableWritableStream } from "./stream.js";
export type PregelLoopInitializeParams = {
    input?: any | Command;
    config: RunnableConfig;
    checkpointer?: BaseCheckpointSaver;
    outputKeys: string | string[];
    streamKeys: string | string[];
    nodes: Record<string, PregelNode>;
    channelSpecs: Record<string, BaseChannel>;
    managed: ManagedValueMapping;
    stream: IterableReadableWritableStream;
    store?: BaseStore;
    interruptAfter: string[] | All;
    interruptBefore: string[] | All;
    manager?: CallbackManagerForChainRun;
    debug: boolean;
};
type PregelLoopParams = {
    input?: any | Command;
    config: RunnableConfig;
    checkpointer?: BaseCheckpointSaver;
    checkpoint: Checkpoint;
    checkpointMetadata: CheckpointMetadata;
    checkpointPreviousVersions: Record<string, string | number>;
    checkpointPendingWrites: CheckpointPendingWrite[];
    checkpointConfig: RunnableConfig;
    channels: Record<string, BaseChannel>;
    managed: ManagedValueMapping;
    step: number;
    stop: number;
    outputKeys: string | string[];
    streamKeys: string | string[];
    nodes: Record<string, PregelNode>;
    checkpointNamespace: string[];
    skipDoneTasks: boolean;
    isNested: boolean;
    manager?: CallbackManagerForChainRun;
    stream: IterableReadableWritableStream;
    store?: AsyncBatchedStore;
    prevCheckpointConfig: RunnableConfig | undefined;
    interruptAfter: string[] | All;
    interruptBefore: string[] | All;
    debug: boolean;
};
export declare class PregelLoop {
    protected input?: any | Command;
    output: any;
    config: LangGraphRunnableConfig;
    protected checkpointer?: BaseCheckpointSaver;
    protected checkpointerGetNextVersion: (current: number | undefined, channel: BaseChannel) => number;
    channels: Record<string, BaseChannel>;
    managed: ManagedValueMapping;
    protected checkpoint: Checkpoint;
    protected checkpointConfig: RunnableConfig;
    checkpointMetadata: CheckpointMetadata;
    protected checkpointNamespace: string[];
    protected checkpointPendingWrites: CheckpointPendingWrite[];
    protected checkpointPreviousVersions: Record<string, string | number>;
    step: number;
    protected stop: number;
    protected outputKeys: string | string[];
    protected streamKeys: string | string[];
    protected nodes: Record<string, PregelNode>;
    protected skipDoneTasks: boolean;
    protected prevCheckpointConfig: RunnableConfig | undefined;
    status: "pending" | "done" | "interrupt_before" | "interrupt_after" | "out_of_steps";
    tasks: Record<string, PregelExecutableTask<any, any>>;
    stream: IterableReadableWritableStream;
    checkpointerPromises: Promise<unknown>[];
    isNested: boolean;
    protected _checkpointerChainedPromise: Promise<unknown>;
    store?: AsyncBatchedStore;
    manager?: CallbackManagerForChainRun;
    interruptAfter: string[] | All;
    interruptBefore: string[] | All;
    toInterrupt: PregelExecutableTask<string, string>[];
    debug: boolean;
    get isResuming(): any;
    constructor(params: PregelLoopParams);
    static initialize(params: PregelLoopInitializeParams): Promise<PregelLoop>;
    protected _checkpointerPutAfterPrevious(input: {
        config: RunnableConfig;
        checkpoint: Checkpoint;
        metadata: CheckpointMetadata;
        newVersions: Record<string, string | number>;
    }): void;
    protected updateManagedValues(key: string, values: any[]): Promise<void>;
    /**
     * Put writes for a task, to be read by the next tick.
     * @param taskId
     * @param writes
     */
    putWrites(taskId: string, writes: PendingWrite<string>[]): void;
    _outputWrites(taskId: string, writes: [string, unknown][], cached?: boolean): void;
    /**
     * Execute a single iteration of the Pregel loop.
     * Returns true if more iterations are needed.
     * @param params
     */
    tick(params: {
        inputKeys?: string | string[];
    }): Promise<boolean>;
    finishAndHandleError(error?: Error): Promise<boolean>;
    acceptPush(task: PregelExecutableTask<string, string>, writeIdx: number, call?: Call): PregelExecutableTask<string, string> | void;
    protected _suppressInterrupt(e?: Error): boolean;
    protected _first(inputKeys: string | string[]): Promise<void>;
    protected _emit(values: [StreamMode, unknown][]): void;
    protected _putCheckpoint(inputMetadata: Omit<CheckpointMetadata, "step" | "parents">): Promise<void>;
    protected _matchWrites(tasks: Record<string, PregelExecutableTask<string, string>>): void;
}
export {};
