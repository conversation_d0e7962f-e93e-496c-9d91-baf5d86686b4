"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageGraph = exports.messagesStateReducer = void 0;
const messages_1 = require("@langchain/core/messages");
const uuid_1 = require("uuid");
const state_js_1 = require("./state.cjs");
/**
 * Prebuilt reducer that combines returned messages.
 * Can handle standard messages and special modifiers like {@link RemoveMessage}
 * instances.
 */
function messagesStateReducer(left, right) {
    const leftArray = Array.isArray(left) ? left : [left];
    const rightArray = Array.isArray(right) ? right : [right];
    // coerce to message
    const leftMessages = leftArray.map(messages_1.coerceMessageLikeToMessage);
    const rightMessages = rightArray.map(messages_1.coerceMessageLikeToMessage);
    // assign missing ids
    for (const m of leftMessages) {
        if (m.id === null || m.id === undefined) {
            m.id = (0, uuid_1.v4)();
            m.lc_kwargs.id = m.id;
        }
    }
    for (const m of rightMessages) {
        if (m.id === null || m.id === undefined) {
            m.id = (0, uuid_1.v4)();
            m.lc_kwargs.id = m.id;
        }
    }
    // merge
    const merged = [...leftMessages];
    const mergedById = new Map(merged.map((m, i) => [m.id, i]));
    const idsToRemove = new Set();
    for (const m of rightMessages) {
        const existingIdx = mergedById.get(m.id);
        if (existingIdx !== undefined) {
            if (m._getType() === "remove") {
                idsToRemove.add(m.id);
            }
            else {
                idsToRemove.delete(m.id);
                merged[existingIdx] = m;
            }
        }
        else {
            if (m._getType() === "remove") {
                throw new Error(`Attempting to delete a message with an ID that doesn't exist ('${m.id}')`);
            }
            mergedById.set(m.id, merged.length);
            merged.push(m);
        }
    }
    return merged.filter((m) => !idsToRemove.has(m.id));
}
exports.messagesStateReducer = messagesStateReducer;
/** @ignore */
class MessageGraph extends state_js_1.StateGraph {
    constructor() {
        super({
            channels: {
                __root__: {
                    reducer: messagesStateReducer,
                    default: () => [],
                },
            },
        });
    }
}
exports.MessageGraph = MessageGraph;
//# sourceMappingURL=message.js.map