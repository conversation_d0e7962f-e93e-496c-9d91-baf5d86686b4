{"version": 3, "file": "read.js", "sourceRoot": "", "sources": ["../../src/pregel/read.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,eAAe,EAGf,mBAAmB,EACnB,gBAAgB,EAChB,iBAAiB,GAElB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAG/C,MAAM,OAAO,WAGX,SAAQ,gBAAgB;IAUxB,8DAA8D;IAC9D,YACE,OAA+B;IAC/B,8DAA8D;IAC9D,MAA2B,EAC3B,QAAiB,KAAK;QAEtB,KAAK,CAAC;YACJ,IAAI,EAAE,CAAC,CAAW,EAAE,MAAsB,EAAE,EAAE,CAC5C,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;SACpE,CAAC,CAAC;QAnBL;;;;mBAAgB,aAAa;WAAC;QAE9B;;;;;WAAgC;QAEhC;;;;mBAAiB,KAAK;WAAC;QAEvB,8DAA8D;QAC9D;;;;;WAA4B;QAa1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;YAChC,CAAC,CAAC,eAAe,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;YACrC,CAAC,CAAC,eAAe,OAAO,GAAG,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,MAAM,CACX,MAAsB,EACtB,OAA+B,EAC/B,KAAc,EACd,MAAmC;QAEnC,MAAM,IAAI,GACR,MAAM,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,KAAK,CACb,uGAAuG,CACxG,CAAC;SACH;QACD,IAAI,MAAM,EAAE;YACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAM,CAAC;SAC1C;aAAM;YACL,OAAO,IAAI,CAAC,OAAO,EAAE,KAAK,CAAM,CAAC;SAClC;IACH,CAAC;CACF;AAED,MAAM,oBAAoB;AACxB,eAAe,CAAC,IAAI,mBAAmB,EAAuB,CAAC;AA0BjE,MAAM,OAAO,UAGX,SAAQ,eAAoD;IA2B5D,YAAY,MAA2C;QACrD,MAAM,EACJ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,WAAW,EACX,IAAI,EACJ,SAAS,EACT,IAAI,GACL,GAAG,MAAM,CAAC;QACX,MAAM,UAAU,GAAG;YACjB,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAClD,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;SAChB,CAAC;QAEF,KAAK,CAAC;YACJ,GAAG,MAAM;YACT,KAAK,EACH,MAAM,CAAC,KAAK;gBACX,oBAAiE;YACpE,MAAM,EAAE;gBACN,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvC,IAAI,EAAE,UAAU;aACjB;SACF,CAAC,CAAC;QAtDL;;;;mBAAgB,YAAY;WAAC;QAE7B;;;;;WAA4C;QAE5C;;;;mBAAqB,EAAE;WAAC;QAExB,8DAA8D;QAC9D;;;;;WAA4B;QAE5B;;;;mBAAsB,EAAE;WAAC;QAEzB;;;;mBAAuC,oBAAoB;WAAC;QAE5D,8DAA8D;QAC9D;;;;mBAA8B,EAAE;WAAC;QAEjC;;;;mBAAoC,EAAE;WAAC;QAEvC;;;;mBAAiB,EAAE;WAAC;QAEpB;;;;;WAA0B;QAE1B;;;;;WAAuB;QAEvB;;;;;WAAgB;QAgCd,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,UAAU;QACR,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,OACE,UAAU,CAAC,MAAM,GAAG,CAAC;YACrB,uDAAuD;YACvD,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,YAAY,YAAY;YACzD,uDAAuD;YACvD,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,YAAY,YAAY,EACzD;YACA,gDAAgD;YAChD,kEAAkE;YAClE,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAmB,CAAC;YAC1D,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACzE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,YAAY,CAClD,cAAc,EACd,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAC3B,CAAC;YACF,UAAU,CAAC,GAAG,EAAE,CAAC;SAClB;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO;QACL,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAoB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/D,OAAO,SAAS,CAAC;SAClB;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAoB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;SACnB;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAoB,EAAE;YAC9C,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;gBACjB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC5C,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjC,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;SACJ;aAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,OAAO,IAAI,gBAAgB,CAAC;gBAC1B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC5C,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjC,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;IACH,CAAC;IAED,IAAI,CAAC,QAAuB;QAC1B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QAED,OAAO,IAAI,UAAU,CAAsB;YACzC,QAAQ,EAAE;gBACR,GAAG,IAAI,CAAC,QAAQ;gBAChB,GAAG,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;aAC5D;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,WAAW,EAAE,IAAI,CAAC,WAAW;SAC9B,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CACF,UAAwB;QAExB,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;YACrC,OAAO,IAAI,UAAU,CAAyC;gBAC5D,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC;gBACtC,KAAK,EAAE,IAAI,CAAC,KAGX;gBACD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;SACJ;aAAM,IAAI,IAAI,CAAC,KAAK,KAAK,oBAAoB,EAAE;YAC9C,OAAO,IAAI,UAAU,CAAyC;gBAC5D,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,iBAAiB,CAAyB,UAAU,CAAC;gBAC5D,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;SACJ;aAAM;YACL,OAAO,IAAI,UAAU,CAAyC;gBAC5D,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;gBAClC,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;SACJ;IACH,CAAC;CACF"}