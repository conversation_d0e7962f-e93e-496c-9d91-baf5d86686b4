"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const globals_1 = require("@jest/globals");
const runner_js_1 = require("./runner.cjs");
(0, globals_1.describe)("PregelRunner", () => {
    // Basic structure test
    (0, globals_1.describe)("constructor", () => {
        (0, globals_1.it)("should initialize without errors", () => {
            const mockLoop = {};
            const runner = new runner_js_1.PregelRunner({ loop: mockLoop });
            (0, globals_1.expect)(runner).toBeInstanceOf(runner_js_1.PregelRunner);
        });
    });
    // Simple behavior test with limited mocking
    (0, globals_1.describe)("timeout option", () => {
        (0, globals_1.it)("should pass timeout option to AbortSignal.timeout", async () => {
            const mockLoop = {
                config: {
                    configurable: {
                        thread_id: "1",
                    },
                },
                tasks: {},
                step: 1,
                isNested: false,
            };
            const timeoutSpy = globals_1.jest.spyOn(AbortSignal, "timeout");
            const runner = new runner_js_1.PregelRunner({ loop: mockLoop });
            try {
                await runner.tick({ timeout: 5000 });
            }
            catch (e) {
                // Ignore errors
            }
            (0, globals_1.expect)(timeoutSpy).toHaveBeenCalledWith(5000);
            timeoutSpy.mockRestore();
        });
    });
    // Testing the onStepWrite callback behavior
    (0, globals_1.describe)("onStepWrite callback", () => {
        (0, globals_1.it)("should call onStepWrite with the step number and writes", async () => {
            // Create a minimal implementation
            const mockOnStepWrite = globals_1.jest.fn();
            const mockLoop = {
                config: {
                    configurable: {
                        thread_id: "1",
                    },
                },
                tasks: {},
                step: 42,
                isNested: false,
            };
            const runner = new runner_js_1.PregelRunner({ loop: mockLoop });
            try {
                await runner.tick({ onStepWrite: mockOnStepWrite });
            }
            catch (e) {
                // Ignore any errors from other parts of the code
            }
            // Verify the callback was called with the correct step number (42)
            (0, globals_1.expect)(mockOnStepWrite).toHaveBeenCalledWith(42, globals_1.expect.any(Array));
        });
    });
});
//# sourceMappingURL=runner.test.js.map