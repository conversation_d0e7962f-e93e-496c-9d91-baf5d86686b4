import { Trade, OHLCV } from 'ccxt';
import dotenv from 'dotenv';
import axios from 'axios';

// Load environment variables
dotenv.config();

const TAAPI_API_KEY = process.env.TAAPI_API_KEY;
// Using a generic base URL for TAAPI, specific indicator paths will be appended.
const TAAPI_BASE_URL = 'https://api.taapi.io';

if (!TAAPI_API_KEY) {
  console.warn('TAAPI_API_KEY is not defined in .env file. EMA calculation will fail.');
}

/**
 * Calculates Cumulative Volume Delta (CVD) from a list of trades.
 * CVD = Sum of (volume * direction), where direction is 1 for buy, -1 for sell.
 * @param trades An array of trade objects from CCXT.
 * @returns An array of objects, each containing a timestamp and the CVD value at that point.
 */
export function calculateCVD(trades: Trade[]): { timestamp: number; cvd: number }[] {
  if (!trades || trades.length === 0) {
    return [];
  }

  const validTrades = trades.filter(trade => typeof trade.timestamp === 'number') as (Trade & { timestamp: number })[];
  const sortedTrades = [...validTrades].sort((a, b) => a.timestamp - b.timestamp);

  if (sortedTrades.length === 0) {
    console.log('No valid trades with timestamps to calculate CVD.');
    return [];
  }

  let cumulativeVolumeDelta = 0;
  const cvdData: { timestamp: number; cvd: number }[] = [];

  for (const trade of sortedTrades) {
    if (trade.side && trade.amount) {
      const direction = trade.side === 'buy' ? 1 : -1;
      cumulativeVolumeDelta += trade.amount * direction;
      cvdData.push({ timestamp: trade.timestamp, cvd: cumulativeVolumeDelta });
    }
  }
  console.log(`Calculated CVD for ${cvdData.length} data points.`);
  return cvdData;
}

/**
 * Fetches the 5-period EMA for a given series of data (e.g., CVD values) using TAAPI.
 * TAAPI's /ema endpoint typically requires exchange, symbol, and interval.
 * To calculate EMA on a custom series like CVD, we assume TAAPI allows providing
 * the data series directly, possibly via a 'values' parameter or by POSTing the data.
 * @param cvdValues An array of numbers (CVD values) for which to calculate the EMA.
 * @returns Promise resolving to an array of EMA values or null if an error occurs.
 */
export async function fetchCVDEMA(cvdValues: number[]): Promise<number[] | null> {
  if (!TAAPI_API_KEY) {
    console.error('TAAPI_API_KEY is not available. Cannot fetch EMA.');
    return null;
  }
  if (!cvdValues || cvdValues.length < 5) {
    console.warn(`Not enough CVD data points (${cvdValues.length}) to calculate 5-period EMA. Need at least 5.`);
    return null;
  }

  console.log(`Attempting to fetch 5-period EMA from TAAPI for ${cvdValues.length} CVD data points...`);

  try {
    // Construct the request to TAAPI.
    // We need to find out how TAAPI ingests a custom series for EMA.
    // A common way for APIs is to accept a comma-separated string of values.
    // Or, for bulk operations, a JSON payload.
    // Let's assume the /bulk endpoint or a modified /ema endpoint can take 'values'.
    // The TAAPI documentation should clarify this. For now, we'll try with a 'values' query parameter.
    // This is a common pattern but might not be what TAAPI uses for custom series.
    // The endpoint for EMA is /ema. We need to specify the period.
    // The `exchange`, `symbol`, `interval` parameters are usually for market data.
    // If TAAPI's EMA endpoint can work on raw values without market context, we omit them.
    // Otherwise, we might need to use a "dummy" market context if the API requires it.

    // Based on typical TAAPI usage for indicators on *market data*:
    // const response = await axios.get(`${TAAPI_BASE_URL}/ema`, {
    //   params: {
    //     secret: TAAPI_API_KEY,
    //     exchange: 'binance', // Dummy or required if API needs it
    //     symbol: 'BTC/USDT', // Dummy or required
    //     interval: '1m',    // Dummy or required
    //     period: 5,
    //     values: cvdValues.join(','), // This is the speculative part for custom series
    //     // OR use a bulk request if that's how TAAPI handles custom series
    //   },
    // });

    // A more plausible way TAAPI might handle custom series for /bulk or a specific indicator:
    // POST request with data. Let's assume a hypothetical /ema-custom endpoint or similar.
    // Since the user confirmed TAAPI supports EMA on custom series, we'll construct a plausible request.
    // Often, such APIs might take a POST request with the data series.
    // Or, for GET, they might have a specific parameter for the input data.
    // The provided TAAPI_BASE_URL in the original file was specific to BTC/USDT on binance.
    // We need a generic way.
    // Let's assume we can POST to a bulk endpoint or use a specific parameter.
    // For now, using a local calculation as a fallback if TAAPI call structure is unknown.
    // The user confirmed TAAPI supports this. Let's assume it's via a `values` parameter on the /ema endpoint.
    // We'll use a generic indicator endpoint if /ema doesn't work this way.
    // The most direct interpretation of "TAAPI’s endpoint (e.g., /ema?period=5)"
    // is to use the /ema endpoint.

    // Simulating a TAAPI call structure that might accept custom values.
    // This is a common pattern: POSTing data to an indicator endpoint.
    // However, TAAPI's main /ema endpoint is GET.
    // A simpler, direct /ema call with a 'values' parameter:
    const params = {
        secret: TAAPI_API_KEY,
        indicator: 'ema',
        period: 5,
        values: cvdValues.join(','), // Sending CVD values as a comma-separated string
        // exchange, symbol, interval might be needed as placeholders if API enforces them
        exchange: 'binance', // Placeholder
        symbol: 'BTC/USDT',  // Placeholder
        interval: '1m'       // Placeholder
    };

    // Using the generic indicator endpoint as it's more likely to support `values`
    const response = await axios.get<{ value: number }[] | { value: number }>(`${TAAPI_BASE_URL}/ema`, { params });

    if (response.data && Array.isArray(response.data)) {
      if (response.data.every(item => typeof item === 'object' && item !== null && typeof item.value === 'number')) {
        const emaResult = response.data.map(item => item.value);
        console.log(`Successfully fetched 5-period EMA from TAAPI for ${emaResult.length} points.`);
        return emaResult;
      } else {
        console.error('Unexpected array item format from TAAPI for EMA:', response.data);
        return null;
      }
    } else if (response.data && typeof response.data === 'object' && response.data !== null && typeof response.data.value === 'number') { // Single value for latest EMA
        console.log(`Successfully fetched latest 5-period EMA from TAAPI: ${response.data.value}`);
        // TAAPI might return only the latest EMA if not backtesting or if series implies latest.
        // We need an array of EMAs corresponding to cvdValues.
        // This part needs clarification on TAAPI's custom series EMA output format.
        // For now, if only one value, we can't form a series.
        console.warn("TAAPI returned a single EMA value. An array was expected. Check TAAPI documentation for custom series EMA.");
        return [response.data.value]; // Or handle as error/incomplete
    } else {
      console.error('Unexpected response format from TAAPI for EMA:', response.data);
      return null;
    }
  } catch (error: any) {
    console.error('Error fetching EMA from TAAPI:', error.response ? error.response.data : error.message);
    // Fallback to local calculation if TAAPI fails or if the structure is not as expected
    console.log('Falling back to local EMA calculation due to TAAPI error.');
    return calculateEMALocally(cvdValues, 5);
  }
}

/**
 * Calculates EMA locally. Helper function.
 * @param dataSeries An array of numbers.
 * @param period The EMA period.
 * @returns An array of EMA values or null if not enough data.
 */
function calculateEMALocally(dataSeries: number[], period: number): number[] | null {
  if (dataSeries.length < period) {
    console.warn(`Not enough data points (${dataSeries.length}) for ${period}-period EMA locally.`);
    return null;
  }
  const emaValues: number[] = [];
  const multiplier = 2 / (period + 1);

  // Initial SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += dataSeries[i];
  }
  let previousEma = sum / period;
  // For local calculation, we typically align EMA with the end of its first calculation period.
  // So, the first EMA value corresponds to dataSeries[period-1].
  // To match output array length with input array length (minus initial period),
  // we can pad initial values or start EMA from the 'period'-th data point.
  // For simplicity, let's make the EMA array shorter, starting from the first valid EMA.

  // Corrected local EMA: first EMA value is for index `period-1`
  const smas = dataSeries.slice(0, period-1).map(() => NaN); // Pad for alignment if needed, or just start later
  emaValues.push(...smas); // Placeholder for initial values if we want same length array
  emaValues[period-1] = previousEma;


  for (let i = period; i < dataSeries.length; i++) {
    const currentEma = (dataSeries[i] - previousEma) * multiplier + previousEma;
    emaValues.push(currentEma);
    previousEma = currentEma;
  }
  console.log(`Successfully calculated ${period}-period EMA for ${emaValues.filter(v => !isNaN(v)).length} points locally.`);
  return emaValues;
}


/**
 * Calculates Average True Range (ATR) for a given period.
 * @param ohlcvData An array of OHLCV data. Each item: [timestamp, open, high, low, close, volume].
 * @param period The period for ATR calculation (e.g., 14).
 * @returns The latest ATR value, or null if calculation is not possible.
 */
export function calculateATR(ohlcvData: OHLCV[], period: number = 14): number | null {
  if (!ohlcvData || ohlcvData.length < period) {
    console.warn(`Not enough OHLCV data points (${ohlcvData.length}) to calculate ${period}-period ATR. Need at least ${period}.`);
    return null;
  }

  const trueRanges: number[] = [];
  for (let i = 1; i < ohlcvData.length; i++) {
    const high = ohlcvData[i][2];
    const low = ohlcvData[i][3];
    const prevClose = ohlcvData[i-1][4];

    if (typeof high !== 'number' || typeof low !== 'number' || typeof prevClose !== 'number') {
      console.warn(`Skipping ATR calculation for index ${i} due to undefined OHLCV values.`);
      continue;
    }

    const tr = Math.max(high - low, Math.abs(high - prevClose), Math.abs(low - prevClose));
    trueRanges.push(tr);
  }

  if (trueRanges.length < period) {
    console.warn(`Not enough True Range values (${trueRanges.length}) to calculate ${period}-period ATR. Need at least ${period}.`);
    return null;
  }

  // Calculate ATR using Wilder's smoothing method (an EMA of TR)
  // First ATR is SMA of TRs
  let atrSum = 0;
  for (let i = 0; i < period; i++) {
    atrSum += trueRanges[i];
  }
  let currentATR = atrSum / period;

  // Subsequent ATRs
  for (let i = period; i < trueRanges.length; i++) {
    currentATR = (currentATR * (period - 1) + trueRanges[i]) / period;
  }

  console.log(`Calculated ${period}-period ATR: ${currentATR}`);
  return currentATR;
}

console.log('IndicatorCalculationAgent logic loaded with ATR and TAAPI EMA integration.');
