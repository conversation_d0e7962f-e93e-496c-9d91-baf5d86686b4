{"version": 3, "file": "tool_node.js", "sourceRoot": "", "sources": ["../../src/prebuilt/tool_node.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,WAAW,EAEX,aAAa,GACd,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAE/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAQ,MAAM,iBAAiB,CAAC;AAQzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoHG;AACH,8DAA8D;AAC9D,MAAM,OAAO,QAAkB,SAAQ,gBAAsB;IAO3D,YACE,KAAmE,EACnE,OAAyB;QAEzB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QACvD,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAX1E;;;;;WAAoE;QAEpE;;;;mBAAmB,IAAI;WAAC;QAExB;;;;mBAAQ,KAAK;WAAC;QAQZ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC;IACpE,CAAC;IAED,8DAA8D;IACpD,KAAK,CAAC,GAAG,CAAC,KAAU,EAAE,MAAsB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;YAClC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YACzB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9C,IAAI,OAAO,EAAE,QAAQ,EAAE,KAAK,IAAI,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC9B,OAAqB,CAAC,UAAU,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,IAAI;gBACF,IAAI,IAAI,KAAK,SAAS,EAAE;oBACtB,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,cAAc,CAAC,CAAC;iBACnD;gBACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAC9B,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,EAC9B,MAAM,CACP,CAAC;gBACF,IACE,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC;oBACvD,SAAS,CAAC,MAAM,CAAC,EACjB;oBACA,OAAO,MAAM,CAAC;iBACf;qBAAM;oBACL,OAAO,IAAI,WAAW,CAAC;wBACrB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,OAAO,EACL,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;wBAC9D,YAAY,EAAE,IAAI,CAAC,EAAG;qBACvB,CAAC,CAAC;iBACJ;gBACD,8DAA8D;aAC/D;YAAC,OAAO,CAAM,EAAE;gBACf,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;oBAC1B,MAAM,CAAC,CAAC;iBACT;gBACD,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE;oBACvB,0EAA0E;oBAC1E,sEAAsE;oBACtE,4EAA4E;oBAC5E,MAAM,CAAC,CAAC;iBACT;gBACD,OAAO,IAAI,WAAW,CAAC;oBACrB,OAAO,EAAE,UAAU,CAAC,CAAC,OAAO,8BAA8B;oBAC1D,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;iBAC5B,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,IAAI,EAAE,CACT,CAAC;QAEF,sFAAsF;QACtF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAM,CAAC;SACtE;QAED,+CAA+C;QAC/C,MAAM,eAAe,GAIf,EAAE,CAAC;QACT,IAAI,aAAa,GAAmB,IAAI,CAAC;QAEzC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;gBACrB,IACE,MAAM,CAAC,KAAK,KAAK,OAAO,CAAC,MAAM;oBAC/B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAC1C;oBACA,IAAI,aAAa,EAAE;wBAChB,aAAa,CAAC,IAAe,CAAC,IAAI,CAAC,GAAI,MAAM,CAAC,IAAe,CAAC,CAAC;qBACjE;yBAAM;wBACL,aAAa,GAAG,IAAI,OAAO,CAAC;4BAC1B,KAAK,EAAE,OAAO,CAAC,MAAM;4BACrB,IAAI,EAAE,MAAM,CAAC,IAAI;yBAClB,CAAC,CAAC;qBACJ;iBACF;qBAAM;oBACL,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBAC9B;aACF;iBAAM;gBACL,eAAe,CAAC,IAAI,CAClB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,EAAE,CACzD,CAAC;aACH;SACF;QAED,IAAI,aAAa,EAAE;YACjB,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACrC;QAED,OAAO,eAAoB,CAAC;IAC9B,CAAC;CACF;AAED,MAAM,UAAU,cAAc,CAC5B,KAAsD;IAEtD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAClC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACzB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE9C,IACE,YAAY,IAAI,OAAO;QACvB,CAAE,OAAqB,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EACpD;QACA,OAAO,OAAO,CAAC;KAChB;SAAM;QACL,OAAO,GAAG,CAAC;KACZ;AACH,CAAC"}