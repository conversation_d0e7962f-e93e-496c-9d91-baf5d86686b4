{"name": "@langchain/langgraph-sdk", "version": "0.0.76", "description": "Client library for interacting with the LangGraph API", "type": "module", "packageManager": "yarn@1.22.19", "scripts": {"clean": "rm -rf dist/ dist-cjs/", "build": "yarn clean && yarn lc_build --create-entrypoints --pre --tree-shaking", "prepack": "yarn run build", "format": "prettier --write src", "lint": "prettier --check src && tsc --noEmit", "test": "vitest", "typedoc": "typedoc && typedoc src/react/index.ts --out docs/react --options typedoc.react.json && typedoc src/auth/index.ts --out docs/auth --options typedoc.auth.json"}, "main": "index.js", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.15", "p-queue": "^6.6.2", "p-retry": "4", "uuid": "^9.0.0"}, "devDependencies": {"@langchain/core": "^0.3.31", "@langchain/scripts": "^0.1.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@tsconfig/recommended": "^1.0.2", "@types/node": "^20.12.12", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/uuid": "^9.0.1", "@vitejs/plugin-react": "^4.4.1", "concat-md": "^0.5.1", "jsdom": "^26.1.0", "msw": "^2.8.2", "prettier": "^3.2.5", "react": "^19.0.0", "react-dom": "^19.0.0", "typedoc": "^0.27.7", "typedoc-plugin-markdown": "^4.4.2", "typescript": "^5.4.5", "vitest": "^3.1.3"}, "peerDependencies": {"@langchain/core": ">=0.2.31 <0.4.0", "react": "^18 || ^19"}, "peerDependenciesMeta": {"react": {"optional": true}, "@langchain/core": {"optional": true}}, "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./client": {"types": {"import": "./client.d.ts", "require": "./client.d.cts", "default": "./client.d.ts"}, "import": "./client.js", "require": "./client.cjs"}, "./auth": {"types": {"import": "./auth.d.ts", "require": "./auth.d.cts", "default": "./auth.d.ts"}, "import": "./auth.js", "require": "./auth.cjs"}, "./react": {"types": {"import": "./react.d.ts", "require": "./react.d.cts", "default": "./react.d.ts"}, "import": "./react.js", "require": "./react.cjs"}, "./react-ui": {"types": {"import": "./react-ui.d.ts", "require": "./react-ui.d.cts", "default": "./react-ui.d.ts"}, "import": "./react-ui.js", "require": "./react-ui.cjs"}, "./react-ui/server": {"types": {"import": "./react-ui/server.d.ts", "require": "./react-ui/server.d.cts", "default": "./react-ui/server.d.ts"}, "import": "./react-ui/server.js", "require": "./react-ui/server.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts", "client.cjs", "client.js", "client.d.ts", "client.d.cts", "auth.cjs", "auth.js", "auth.d.ts", "auth.d.cts", "react.cjs", "react.js", "react.d.ts", "react.d.cts", "react-ui.cjs", "react-ui.js", "react-ui.d.ts", "react-ui.d.cts", "react-ui/server.cjs", "react-ui/server.js", "react-ui/server.d.ts", "react-ui/server.d.cts"]}