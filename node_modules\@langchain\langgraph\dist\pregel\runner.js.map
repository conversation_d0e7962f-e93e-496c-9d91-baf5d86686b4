{"version": 3, "file": "runner.js", "sourceRoot": "", "sources": ["../../src/pregel/runner.ts"], "names": [], "mappings": "AACA,OAAO,EACL,IAAI,GAIL,MAAM,YAAY,CAAC;AACpB,OAAO,EACL,mBAAmB,EACnB,iBAAiB,GAElB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EACL,eAAe,EACf,qBAAqB,EACrB,IAAI,EACJ,KAAK,EACL,SAAS,EACT,MAAM,EACN,SAAS,EACT,UAAU,EACV,MAAM,EACN,eAAe,EACf,wBAAwB,GACzB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAiB,eAAe,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAChF,OAAO,EAAE,aAAa,EAAqB,MAAM,YAAY,CAAC;AAiC9D;;GAEG;AACH,MAAM,OAAO,YAAY;IAKvB;;;OAGG;IACH,YAAY,EACV,IAAI,EACJ,YAAY,GAIb;QAdD;;;;;WAA4C;QAE5C;;;;;WAAyB;QAavB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,IAAI,CAAC,UAAuB,EAAE;QAClC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAEtE,MAAM,UAAU,GAAe,IAAI,GAAG,EAAE,CAAC;QACzC,IAAI,aAAwC,CAAC;QAC7C,MAAM,yBAAyB,GAAG,IAAI,eAAe,EAAE,CAAC;QAExD,uBAAuB;QACvB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CACxD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAC7B,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAClD,yBAAyB;YACzB,OAAO;YACP,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE;YAC3D,OAAO,EAAE,cAAc;YACvB,WAAW;YACX,cAAc;SACf,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,IAAI,UAAU,EAAE;YAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1B,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBAC3B,aAAa,GAAG,KAAK,CAAC;aACvB;iBAAM,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE;gBACrE,aAAa,GAAG,KAAK,CAAC;aACvB;iBAAM,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC7D;;;;;;;;;;;;;mBAaG;gBACH,yBAAyB,CAAC,KAAK,EAAE,CAAC;gBAClC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;aACvB;SACF;QAED,WAAW,EAAE,CACX,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;aAC3B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;aAC1B,IAAI,EAAE,CACV,CAAC;QAEF,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE;YACzB,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SACjC;aAAM,IAAI,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE;YAC9B,MAAM,IAAI,cAAc,CACtB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EACtB,6CAA6C,IAAI,CAAC,IAAI,CAAC,IAAI,8DAA8D,CAC1H,CAAC;SACH;QAED,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE;YACnC,MAAM,aAAa,CAAC;SACrB;QAED,IAAI,eAAe,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACxD,MAAM,aAAa,CAAC;SACrB;IACH,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,uBAAuB,CAAC,EAC9B,yBAAyB,EACzB,OAAO,EACP,MAAM,GAKP;QACC,MAAM,eAAe,GAClB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAC9B,wBAAwB,CACF,IAAI,EAAE,CAAC;QAEjC,6FAA6F;QAC7F,8DAA8D;QAC9D,MAAM,qCAAqC,GACzC,MAAM;YACN,eAAe,CAAC,mBAAmB;YACnC,MAAM,KAAK,eAAe,CAAC,mBAAmB,CAAC;QAEjD,MAAM,mBAAmB,GAAG,qCAAqC;YAC/D,CAAC,CAAC,8FAA8F;gBAC9F,8CAA8C;gBAC9C,mBAAmB,CAAC,eAAe,CAAC,mBAAoB,EAAE,MAAO,CAAC;YACpE,CAAC,CAAC,2FAA2F;gBAC3F,eAAe;gBACf,eAAe,CAAC,mBAAmB,IAAI,MAAM,CAAC;QAElD,MAAM,gBAAgB,GAAG,eAAe,CAAC,gBAAgB;YACvD,CAAC,CAAC,0FAA0F;gBAC1F,sEAAsE;gBACtE,mBAAmB,CACjB,eAAe,CAAC,gBAAiB,EACjC,yBAAyB,CAAC,MAAM,CACjC;YACH,CAAC,CAAC,yBAAyB,CAAC,MAAM,CAAC;QAErC,MAAM,kBAAkB,GAAG,OAAO;YAChC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC;YAC9B,CAAC,CAAC,SAAS,CAAC;QAEd,MAAM,mBAAmB,GAAgB,mBAAmB,CAC1D,GAAG,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EACrD,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EACnD,gBAAgB,CACjB,CAAC;QAEF,MAAM,cAAc,GAAuB;YACzC,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;YAClB,mBAAmB;SACpB,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACrD,CAAC,wBAAwB,CAAC,EAAE,cAAc;SAC3C,CAAC,CAAC;QAEH,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,CAAC,sBAAsB,CACnC,KAA6C,EAC7C,OAIC;QAED,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;QAE/D,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEtD,IAAI,kBAA8B,CAAC;QAEnC,IAAI,gBAAoD,CAAC;QACzD,SAAS,WAAW,CAAC,OAAiC;YACpD,kBAAkB,GAAG,GAAG,EAAE;gBACxB,gBAAgB,GAAG,IAAI,OAAO,CAAC,WAAW,CAEzC,CAAC;gBACF,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC9B,CAAC,CAAC;QACJ,CAAC;QAED,gBAAgB,GAAG,IAAI,OAAO,CAAC,WAAW,CAEzC,CAAC;QAEF,MAAM,iBAAiB,GAOnB,EAAE,CAAC;QAEP,SAAS,MAAM,CACb,MAAoB,EACpB,IAA0C,EAC1C,MAAgC,EAChC,EAAE,KAAK,KAAyB,EAAE;YAElC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE;gBACjD,OAAO,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACrE;YAED,wCAAwC;YACxC,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,qBAAqB,CAEvD,CAAC;YAEd,IAAI,CAAC,UAAU,EAAE;gBACf,MAAM,IAAI,KAAK,CACb,oCAAoC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,CAC5D,CAAC;aACH;YAED,MAAM,GAAG,GAAiD,EAAE,CAAC;YAE7D,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;gBAC3C,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;gBACxB,IAAI,OAAO,KAAK,IAAI,EAAE;oBACpB,SAAS;iBACV;gBAED,MAAM,KAAK,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC3B,MAAM,GAAG,GAAG,UAAU,CAAC,WAAW,CAAC;gBACnC,UAAU,CAAC,WAAW,IAAI,CAAC,CAAC;gBAE5B,IAAI,KAAK,IAAI,IAAI,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;iBACvC;gBAED,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;gBAE1D,IAAI,CAAC,QAAQ,EAAE;oBACb,SAAS;iBACV;gBAED,wCAAwC;gBACxC,MAAM,eAAe,GAAG,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAEvD,IAAI,eAAe,KAAK,SAAS,EAAE;oBACjC,yEAAyE;oBACzE,GAAG,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;iBAC5B;qBAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrC,uCAAuC;oBACvC,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;oBAC9D,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;oBAE5D,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;wBACtB,8BAA8B;wBAC9B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;4BACxB,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;yBAC3C;6BAAM;4BACL,wBAAwB;4BACxB,MAAM,IAAI,KAAK,CACb,wCAAwC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,EAAE,CACxE,CAAC;yBACH;qBACF;yBAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;wBAC5B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;4BACvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChC,cAAc;4BACd,MAAM,KAAK;4BACT,uDAAuD;4BACvD,UAAU,YAAY,KAAK;gCACzB,CAAC,CAAC,UAAU;gCACZ,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;4BAEpC,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;yBAClC;6BAAM;4BACL,mGAAmG;4BACnG,MAAM,IAAI,KAAK,CACb,uCAAuC,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,EAAE,CACvE,CAAC;yBACH;qBACF;iBACF;qBAAM;oBACL,oCAAoC;oBACpC,MAAM,IAAI,GAAG,aAAa,CAAiB,QAAQ,EAAE,WAAW,EAAE;wBAChE,CAAC,eAAe,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;wBACtD,mEAAmE;wBACnE,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;qBACrD,CAAC,CAAC;oBAEH,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;oBACtC,kBAAkB,EAAE,CAAC;oBAErB,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE;wBACzC,IAAI,KAAK,EAAE;4BACT,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;yBAC9B;wBAED,OAAO,MAAM,CAAC;oBAChB,CAAC,CAAC,CAAC;iBACJ;aACF;YAED,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QAED,SAAS,IAAI,CACX,MAAoB,EACpB,IAA0C,EAC1C,IAAwD,EACxD,IAAY,EACZ,KAAc,EACd,UAAwD,EAAE;YAE1D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;gBAClD,KAAK,EAAE;oBACL,IAAI,IAAI,CAAC;wBACP,IAAI;wBACJ,IAAI;wBACJ,KAAK;wBACL,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,SAAS,EAAE,OAAO,CAAC,SAAS;qBAC7B,CAAC;iBACH;aACF,CAAC,CAAC;YAEH,uDAAuD;YACvD,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBACvB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;iBAClB;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;aAC5B;YAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,OAAO,EAAE,mBAAmB,EAAE,OAAO,EAAE;YACzC,wEAAwE;YACxE,2DAA2D;YAC3D,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;SAC1B;QAED,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,IAAI,QAAoB,CAAC;QACzB,MAAM,qBAAqB,GACzB,OAAO,EAAE,mBAAmB,IAAI,OAAO,EAAE,kBAAkB;YACzD,CAAC,CAAC,mBAAmB,CACjB,GAAG,CAAC,OAAO,CAAC,mBAAmB;gBAC7B,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;gBAC/B,CAAC,CAAC,EAAE,CAAC,EACP,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACpE;YACH,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,YAAY,GAAG,qBAAqB;YACxC,CAAC,CAAC,IAAI,OAAO,CAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;gBACtC,QAAQ,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBAC5C,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE;oBACxD,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;YACL,CAAC,CAAC;YACJ,CAAC,CAAC,SAAS,CAAC;QAEd,OACE,CAAC,iBAAiB,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;YACtE,KAAK,CAAC,MAAM,EACZ;YACA,OAEE,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,MAAM;gBACrC,CAAC,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,iBAAiB,GAAG,KAAK,CAAC,MAAM,EACtE,iBAAiB,IAAI,CAAC,EACtB;gBACA,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBAEtC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,aAAa,CACxC,IAAI,EACJ,WAAW,EACX;oBACE,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;oBACjD,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;iBAChD,EACD,OAAO,EAAE,mBAAmB,CAC7B,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBAChB,OAAO;wBACL,IAAI;wBACJ,KAAK;wBACL,aAAa,EAAE,OAAO,EAAE,mBAAmB,EAAE,OAAO;qBACrD,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;YAED,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBACrC,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBACnC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvC,gBAAgB;aACjB,CAAC,CAAC;YAEH,IAAI,WAAW,KAAK,kBAAkB,EAAE;gBACtC,SAAS;aACV;YAED,MAAM,WAAgC,CAAC;YACvC,OAAO,iBAAiB,CAAE,WAAiC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACtE;IACH,CAAC;IAED;;;;;;;OAOG;IACK,OAAO,CAAC,IAA0C,EAAE,KAAa;QACvE,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;oBAC3B,MAAM,UAAU,GAA2B,KAAK,CAAC,UAAU,CAAC,GAAG,CAC7D,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CACtC,CAAC;oBACF,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC;oBAC3D,IAAI,OAAO,CAAC,MAAM,EAAE;wBAClB,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;qBAC7B;oBACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;iBAC1C;aACF;iBAAM,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACvD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE;oBAC3B,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;iBACtD,CAAC,CAAC;aACJ;SACF;aAAM;YACL,IACE,IAAI,CAAC,YAAY;gBACjB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EACrE;gBACA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC5B,uBAAuB;gBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;aACrC;YAED,mCAAmC;YACnC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SAC3C;IACH,CAAC;CACF"}