{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/managed/base.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iBAAiB,CAAC;AAKtD,8DAA8D;AAC9D,MAAM,OAAgB,YAAY;IAShC,YAAY,MAAsB,EAAE,OAA4B;QARhE;;;;mBAAmB,KAAK;WAAC;QAEzB;;;;;WAAuB;QAEvB;;;;mBAAwC,EAAE;WAAC;QAE3C;;;;mBAAsB,IAAI;WAAC;QAGzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,8DAA8D;IAC9D,MAAM,CAAC,KAAK,CAAC,UAAU,CACrB,OAAuB;IACvB,8DAA8D;IAC9D,KAAW;QAEX,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACrC,CAAC;IAID,KAAK,CAAC,QAAQ;QACZ,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC;IAES,UAAU,CAAC,OAAyB;QAC5C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,OAAgB,oBAKpB,SAAQ,YAAmB;CAE5B;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,6BAA6B,CAAC;AAWnE,8DAA8D;AAC9D,MAAM,OAAO,mBAAoB,SAAQ,GAA8B;IACrE,8DAA8D;IAC9D,YAAY,OAAsD;QAChE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,8DAA8D;IAC9D,oBAAoB,CAAC,IAAY,EAAE,MAAiC;QAClE,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,OAAO;SACR;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;YACxD,OAAO;SACR;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACxD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACjD,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;oBACvC,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;wBACzC,6CAA6C;wBAC7C,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC;qBAC/C;iBACF;aACF;SACF;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,aAAa,IAAI,MAAM,EAAE;YAChE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAC1C,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAC9B,EAAE;gBACD,IAAI;oBACF,8DAA8D;oBAC9D,MAAM,KAAK,GAAI,MAAc,CAAC,GAAG,CAAC,CAAC;oBACnC,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;wBACvC,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;4BACzC,iFAAiF;4BAChF,MAAc,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC;yBACxD;qBACF;oBACD,8DAA8D;iBAC/D;gBAAC,OAAO,KAAU,EAAE;oBACnB,sBAAsB;oBACtB,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;wBACjC,MAAM,KAAK,CAAC;qBACb;iBACF;aACF;SACF;IACH,CAAC;IAED,0BAA0B,CACxB,IAAY;IACZ,8DAA8D;IAC9D,MAAiC;QAEjC,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,OAAO;SACR;QAED,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;YACxD,OAAO;SACR;QAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACxD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACjD,IACE,OAAO,KAAK,KAAK,QAAQ;oBACzB,KAAK,KAAK,IAAI;oBACd,mBAAmB,IAAI,KAAK,EAC5B;oBACA,MAAM,WAAW,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC;oBAC/C,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;wBACnC,6CAA6C;wBAC7C,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;qBACjD;iBACF;aACF;SACF;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,aAAa,IAAI,MAAM,EAAE;YAChE,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAC1C,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAC9B,EAAE;gBACD,IAAI;oBACF,8DAA8D;oBAC9D,MAAM,KAAK,GAAI,MAAc,CAAC,GAAG,CAAC,CAAC;oBACnC,IACE,OAAO,KAAK,KAAK,QAAQ;wBACzB,KAAK,KAAK,IAAI;wBACd,mBAAmB,IAAI,KAAK,EAC5B;wBACA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;wBAC1D,IAAI,YAAY,EAAE;4BAChB,iFAAiF;4BAChF,MAAc,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAChD;qBACF;oBACD,8DAA8D;iBAC/D;gBAAC,OAAO,KAAU,EAAE;oBACnB,sBAAsB;oBACtB,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;wBACjC,MAAM,KAAK,CAAC;qBACb;iBACF;aACF;SACF;IACH,CAAC;CACF;AAED,MAAM,UAAU,cAAc,CAAC,KAAc;IAC3C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,qBAAqB,IAAI,KAAK,EAAE;QACxE,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,wBAAwB,CACtC,KAAc;IAEd,IACE,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK;QACL,KAAK,IAAI,KAAK;QACd,QAAQ,IAAI,KAAK,EACjB;QACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAChD,IAAI,KAAI,CAAC;IAET,MAAM,CAAC,KAAK,CAAC,UAAU,CACrB,MAAsB;IACtB,8DAA8D;IAC9D,KAAW;QAEX,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACvD,CAAC;CACF"}