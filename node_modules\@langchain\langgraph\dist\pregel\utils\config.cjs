"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getParentCheckpointNamespace = exports.recastCheckpointNamespace = exports.getCurrentTaskInput = exports.getConfig = exports.getWriter = exports.getStore = exports.ensureLangGraphConfig = void 0;
const singletons_1 = require("@langchain/core/singletons");
const constants_js_1 = require("../../constants.cjs");
const COPIABLE_KEYS = ["tags", "metadata", "callbacks", "configurable"];
const CONFIG_KEYS = [
    "tags",
    "metadata",
    "callbacks",
    "runName",
    "maxConcurrency",
    "recursionLimit",
    "configurable",
    "runId",
    "outputKeys",
    "streamMode",
    "store",
    "writer",
    "interruptBefore",
    "interruptAfter",
    "signal",
];
const DEFAULT_RECURSION_LIMIT = 25;
function ensureLangGraphConfig(...configs) {
    const empty = {
        tags: [],
        metadata: {},
        callbacks: undefined,
        recursionLimit: DEFAULT_RECURSION_LIMIT,
        configurable: {},
    };
    const implicitConfig = singletons_1.AsyncLocalStorageProviderSingleton.getRunnableConfig();
    if (implicitConfig !== undefined) {
        for (const [k, v] of Object.entries(implicitConfig)) {
            if (v !== undefined) {
                if (COPIABLE_KEYS.includes(k)) {
                    let copiedValue;
                    if (Array.isArray(v)) {
                        copiedValue = [...v];
                    }
                    else if (typeof v === "object") {
                        if (k === "callbacks" &&
                            "copy" in v &&
                            typeof v.copy === "function") {
                            copiedValue = v.copy();
                        }
                        else {
                            copiedValue = { ...v };
                        }
                    }
                    else {
                        copiedValue = v;
                    }
                    empty[k] = copiedValue;
                }
                else {
                    empty[k] = v;
                }
            }
        }
    }
    for (const config of configs) {
        if (config === undefined) {
            continue;
        }
        for (const [k, v] of Object.entries(config)) {
            if (v !== undefined && CONFIG_KEYS.includes(k)) {
                empty[k] = v;
            }
        }
    }
    for (const [key, value] of Object.entries(empty.configurable)) {
        empty.metadata = empty.metadata ?? {};
        if (!key.startsWith("__") &&
            (typeof value === "string" ||
                typeof value === "number" ||
                typeof value === "boolean") &&
            !(key in empty.metadata)) {
            empty.metadata[key] = value;
        }
    }
    return empty;
}
exports.ensureLangGraphConfig = ensureLangGraphConfig;
/**
 * A helper utility function that returns the {@link BaseStore} that was set when the graph was initialized
 *
 * @returns a reference to the {@link BaseStore} that was set when the graph was initialized
 */
function getStore() {
    const config = singletons_1.AsyncLocalStorageProviderSingleton.getRunnableConfig();
    return config?.store;
}
exports.getStore = getStore;
/**
 * A helper utility function that returns the {@link LangGraphRunnableConfig#writer} if "custom" stream mode is enabled, otherwise undefined
 *
 * @returns a reference to the {@link LangGraphRunnableConfig#writer} if "custom" stream mode is enabled, otherwise undefined
 */
function getWriter() {
    const config = singletons_1.AsyncLocalStorageProviderSingleton.getRunnableConfig();
    return config?.configurable?.writer;
}
exports.getWriter = getWriter;
/**
 * A helper utility function that returns the {@link LangGraphRunnableConfig} that was set when the graph was initialized
 *
 * @returns the {@link LangGraphRunnableConfig} that was set when the graph was initialized
 */
function getConfig() {
    return singletons_1.AsyncLocalStorageProviderSingleton.getRunnableConfig();
}
exports.getConfig = getConfig;
/**
 * A helper utility function that returns the input for the currently executing task
 *
 * @returns the input for the currently executing task
 */
function getCurrentTaskInput() {
    const config = singletons_1.AsyncLocalStorageProviderSingleton.getRunnableConfig();
    if (config === undefined) {
        throw new Error("Config not retrievable. This is likely because you are running in an environment without support for AsyncLocalStorage.");
    }
    if (config.configurable?.[constants_js_1.CONFIG_KEY_SCRATCHPAD]?.currentTaskInput === undefined) {
        throw new Error("BUG: internal scratchpad not initialized.");
    }
    return config.configurable[constants_js_1.CONFIG_KEY_SCRATCHPAD].currentTaskInput;
}
exports.getCurrentTaskInput = getCurrentTaskInput;
function recastCheckpointNamespace(namespace) {
    return namespace
        .split(constants_js_1.CHECKPOINT_NAMESPACE_SEPARATOR)
        .filter((part) => !part.match(/^\d+$/))
        .map((part) => part.split(constants_js_1.CHECKPOINT_NAMESPACE_END)[0])
        .join(constants_js_1.CHECKPOINT_NAMESPACE_SEPARATOR);
}
exports.recastCheckpointNamespace = recastCheckpointNamespace;
function getParentCheckpointNamespace(namespace) {
    const parts = namespace.split(constants_js_1.CHECKPOINT_NAMESPACE_SEPARATOR);
    while (parts.length > 1 && parts[parts.length - 1].match(/^\d+$/)) {
        parts.pop();
    }
    return parts.slice(0, -1).join(constants_js_1.CHECKPOINT_NAMESPACE_SEPARATOR);
}
exports.getParentCheckpointNamespace = getParentCheckpointNamespace;
//# sourceMappingURL=config.js.map